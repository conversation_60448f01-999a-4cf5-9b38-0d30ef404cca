/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { pollen_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof pollen_v1.Pollen;
};
export declare function pollen(version: 'v1'): pollen_v1.Pollen;
export declare function pollen(options: pollen_v1.Options): pollen_v1.Pollen;
declare const auth: AuthPlus;
export { auth };
export { pollen_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
