{"ALL_COMPILER_OPTIONS_6917": "ALLE COMPILEROPTIONEN", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Ein Modifizierer \"{0}\" darf nicht mit einer Importdeklaration verwendet werden.", "A_0_parameter_must_be_the_first_parameter_2680": "Ein \"{0}\"-Parameter muss der erste Parameter sein.", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Ein JSDoc-Kommentar \"@typedef\" darf nicht mehrere @type-Tags enthalten.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Ein bigint-Literal kann keine exponentielle Notation verwenden.", "A_bigint_literal_must_be_an_integer_1353": "Ein bigint-Literal muss eine ganze Zahl sein.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Ein Bindungsmusterparameter darf in einer Implementierungssignatur nicht optional sein.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Eine break-Anweisung darf nur in einer einschließenden iteration- oder switch-Anweisung verwendet werden.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Eine break-<PERSON>weisung kann nur zu einer Bezeichnung einer einschließenden Anweisung springen.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Eine Klasse kann nur einen Bezeichner/\"qualified-name\" mit optionalen Typargumenten implementieren.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Eine Klasse kann nur einen Objekttyp oder eine Schnittmenge von Objekttypen mit statisch bekannten Membern implementieren.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Eine Klassendeklaration ohne den default-Modifizierer muss einen Namen besitzen.", "A_class_member_cannot_have_the_0_keyword_1248": "Ein Klassenmember darf nicht das Schlüsselwort \"{0}\" aufweisen.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Ein Kommaausdruck ist in einem berechneten Eigenschaftennamen unzulässig.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Ein berechneter Eigenschaftenname kann nicht aus seinem enthaltenden Typ auf einen Typparameter verweisen.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Ein berechneter Eigenschaftenname in einer Klasseneigenschaftsdeklaration muss einen einfachen Literaltyp oder den Typ \"unique symbol\" auf<PERSON>sen.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Ein berechneter Eigenschaftenname in einer Methodenüberladung muss auf einen Ausdruck verweisen, dessen Typ ein Literal oder ein \"unique symbol\"-Typ ist.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Ein berechneter Eigenschaftenname in einem Typliteral muss auf einen Ausdruck verweisen, dessen Typ ein Literal oder ein \"unique symbol\"-Typ ist.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Ein berechneter Eigenschaftenname in einem Umgebungskontext muss auf einen Ausdruck verweisen, dessen Typ ein Literal oder ein \"unique symbol\"-Typ ist.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Ein berechneter Eigenschaftenname in einer Schnittstelle muss auf einen Ausdruck verweisen, dessen Typ ein Literal oder ein \"unique symbol\"-Typ ist.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Ein berechneter Eigenschaftenname muss vom Typ \"string\", \"number\", \"symbol\" oder \"any\" sein.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Eine const-Assertion kann nur auf Verweise auf Enumerationsmember oder Zeichenfolgen-, <PERSON>ahlen-, booles<PERSON>, Array- oder Objektliterale angewendet werden.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Auf einen const-Enumerationsmember kann nur mithilfe eines Zeichenfolgenliterals zugegriffen werden.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Bei einem const-Initialisierer in einem Umgebungskontext muss es sich um ein Zeichenfolgen- oder um ein numerisches Literal oder um einen Verweis auf ein Enumerationsliteral handeln.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Ein Konstruktor darf keinen super-<PERSON><PERSON><PERSON><PERSON> enthalten, wenn seine Klasse \"null\" erweitert.", "A_constructor_cannot_have_a_this_parameter_2681": "Ein Konstruktor darf keinen \"this\"-<PERSON><PERSON> aufweisen.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Eine continue-Anweisung darf nur in einer einschließenden iteration-Anweisung verwendet werden.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Eine continue-Anweisun<PERSON> kann nur zu einer Bezeichnung einer einschließenden Iterationsanweisung springen.", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Ein declare-Modifizierer darf nicht in einem Kontext verwendet werden, der bereits ein Umgebungskontext ist.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Ein Decorator-Element kann nur für eine Methodenimplementierung und nicht für eine Überladung verwendet werden.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Eine default-<PERSON><PERSON> darf nicht mehrmals in einer switch-Anweisung auftreten.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Ein Standardexport kann nur in einem Modul des Typs ECMAScript verwendet werden.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Ein Standardexport muss sich auf der obersten Ebene einer Datei- oder Moduldeklaration befinden.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Eine definitive Zuweisungsassertion \"!\" ist in diesem Kontext nicht zulässig.", "A_destructuring_declaration_must_have_an_initializer_1182": "Eine destrukturierende Deklaration muss einen Initialisierer besitzen.", "A_dynamic_import_call_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_declarat_2712": "Ein dynamischer Importaufruf in ES5/ES3 er<PERSON>ert den Konstruktor \"Promise\".  <PERSON><PERSON><PERSON>, dass Sie über eine Deklaration für den Konstruktor \"Promise\" verfügen, oder schließen Sie \"ES2015\" in Ihre Option \"--lib\" ein.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Ein dynamischer Importaufruf gibt \"Promise\" zurück. <PERSON><PERSON><PERSON>, dass Sie über eine Deklaration für \"Promise\" verfügen, oder schließen Sie ES2015 in Ihre Option \"--lib\" ein.", "A_file_cannot_have_a_reference_to_itself_1006": "Eine Datei darf keinen Verweis auf sich selbst enthalten.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "<PERSON><PERSON>, die \"never\" zurückgibt, kann keinen erreichbaren Endpunkt besitzen.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "<PERSON><PERSON>, die mit dem Schlüsselwort \"new\" aufgerufen wird, darf keinen \"this\"-<PERSON><PERSON> auf<PERSON>, der \"void\" ist.", "A_function_whose_declared_type_is_neither_void_nor_any_must_return_a_value_2355": "<PERSON><PERSON>, deren <PERSON> weder als \"void\" noch als \"any\" deklariert ist, muss einen Wert zurückgeben.", "A_generator_cannot_have_a_void_type_annotation_2505": "Ein Generator darf keine void-Typanmerkung aufweisen.", "A_get_accessor_cannot_have_parameters_1054": "Eine get-Zugriffsmethode darf keine Parameter haben.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Eine Get-Zugriffsmethode muss mindestens so zugänglich sein wie der Setter.", "A_get_accessor_must_return_a_value_2378": "Eine get-Zugriffsmethode muss einen Wert zurückgeben.", "A_label_is_not_allowed_here_1344": "Eine Bezeichnung ist hier nicht zul<PERSON>ssig.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Ein bezeichnetes Tupelelement wird optional mit einem Fragezeichen nach dem Namen und vor dem Doppelpunkt deklariert, nicht nach dem Typ.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Ein bezeichnetes Tupelelement wird mit \"...\" vor dem Namen und nicht vor dem Typ als \"rest\" deklariert.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Ein zugeordneter Typ darf keine Eigenschaften oder Methoden deklarieren.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Ein Memberinitialisierer in einer Enumerationsdeklaration darf nicht auf Member verweisen, die anschließend deklariert werden (einschließlich Member, die in anderen Enumerationen definiert sind).", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Eine Mixin-Klasse benötigt einen Konstruktor mit einem einzelnen REST-Parameter des Typs \"any[]\".", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Eine Mixin-Klasse, die aus einer Typvariable mit einer abstrakten Konstruktsignatur erweitert wird, muss auch als \"abstract\" deklariert werden.", "A_module_cannot_have_multiple_default_exports_2528": "Ein Modul darf nicht mehrere Standardexporte aufweisen.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Eine Namespacedeklaration darf sich nicht in einer anderen Datei als die Klasse oder Funktion befinden, mit der sie zusammengeführt wird.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Eine Namespacedeklaration darf nicht vor der Klasse oder Funktion positioniert werden, mit der sie zusammengeführt wird.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Eine Namespacedeklaration ist nur auf der obersten Ebene eines Namespaces oder Moduls zulässig.", "A_non_dry_build_would_build_project_0_6357": "Bei einem Build ohne das Flag \"-dry\" würde das Projekt \"{0}\" erstellt.", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Bei einem Build ohne das Flag \"-dry\" würden die folgenden Dateien gelöscht: {0}", "A_non_dry_build_would_update_output_of_project_0_6375": "Ein Build ohne das Flag \"-dry\" würde die Ausgabe des Projekts \"{0}\" aktualisieren.", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Ein Build ohne das Flag \"-dry\" würde die Zeitstempel der Ausgabe von Projekt \"{0}\" aktualisieren.", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Ein Parameterinitialisierer ist nur in einer Funktions- oder Konstruktorimplementierung zulässig.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Eine Parametereigenschaft darf nicht mithilfe eines rest-Parameters deklariert werden.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Eine Parametereigenschaft ist nur in einer Konstruktorimplementierung zulässig.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Eine Parametereigenschaft darf nicht mithilfe eines Bindungsmusters deklariert werden.", "A_promise_must_have_a_then_method_1059": "Ein Zusage muss eine \"then\"-<PERSON><PERSON> aufweisen.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Eine Eigenschaft einer Klasse, deren <PERSON> ein \"unique symbol\"-<PERSON><PERSON> ist, muss sowohl \"static\" als auch \"readonly\" sein.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Eine Eigenschaft einer Schnittstelle oder eines Typliterals, deren <PERSON> ein \"unique symbol\"-<PERSON><PERSON> ist, muss \"readonly\" sein.", "A_required_element_cannot_follow_an_optional_element_1257": "Ein erforderliches Element kann nicht auf ein optionales Element folgen.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Ein erforderlicher Parameter darf nicht auf einen optionalen Parameter folgen.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Ein rest-Element darf kein Bindungsmuster enthalten.", "A_rest_element_cannot_follow_another_rest_element_1265": "Ein rest-Element darf nicht auf ein anderes rest-Element folgen.", "A_rest_element_cannot_have_a_property_name_2566": "Ein rest-Element darf keinen Eigenschaftennamen aufweisen.", "A_rest_element_cannot_have_an_initializer_1186": "Ein rest-Element darf keinen Initialisierer aufweisen.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Ein rest-Element muss das letzte Element in einem Destrukturierungsmuster sein.", "A_rest_element_type_must_be_an_array_type_2574": "Ein rest-Elementtyp muss ein Arraytyp sein.", "A_rest_parameter_cannot_be_optional_1047": "Ein rest-Parameter darf nicht optional sein.", "A_rest_parameter_cannot_have_an_initializer_1048": "Ein rest-Parameter darf keinen Initialisierer aufweisen.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Ein rest-Parameter muss in einer Parameterliste der letzte Eintrag sein.", "A_rest_parameter_must_be_of_an_array_type_2370": "Ein rest-Parameter muss ein <PERSON> sein.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Ein rest-Parameter oder ein Bindungsmuster dürfen kein nachgestelltes Komma aufweisen.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Eine return-<PERSON><PERSON><PERSON><PERSON> kann nur in einem Funktionstext verwendet werden.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Eine \"return\"-<PERSON><PERSON><PERSON><PERSON> kann nicht innerhalb eines statischen Klassenblocks verwendet werden.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "<PERSON><PERSON> Reihe von <PERSON>, die Importe zum Nachschlagen von S<PERSON>icherorten in Bezug auf die \"baseUrl\" neu zuordnen.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Eine set-Zugriffsmethode darf keine Rückgabetypanmerkung aufweisen.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Eine set-Zugriffsmethode darf keinen optionalen Parameter aufweisen.", "A_set_accessor_cannot_have_rest_parameter_1053": "Eine set-Zugriffsmethode darf keinen rest-Parameter aufweisen.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Eine set-Zugriffsmethode muss genau einen Parameter aufweisen.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Ein set-Zugriffsmethodenparameter darf keinen Initialisierer aufweisen.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Ein Überfüllungsargument muss entweder einen Tupeltyp aufweisen oder an einen Restparameter übergeben werden.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Ein „super“ Aufruf muss eine Anweisung auf Stammebene innerhalb eines Konstruktors einer abgeleiteten Klasse sein, die initialisierte Eigenschaften, Parametereigenschaften oder private Bezeichner enthält.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Ein „super“ Aufruf muss die erste Anweisung im Konstruktor sein, um auf „super“ oder „this“ zu verweisen, wenn eine abgeleitete Klasse initialisierte Eigenschaften, Parametereigenschaften oder private Bezeichner enthält.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Ein auf \"this\" basiere<PERSON>pwächter ist nicht mit einem parameterbasierten Typwächter kompatibel.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Ein this-Typ ist nur in einem nicht statischen Member einer Klasse oder Schnittstelle verfügbar.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "<PERSON><PERSON> \"tsconfig.json\" ist bereits definiert unter: \"{0}\".", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Ein Tupelelement kann nicht gleichzeitig als \"optional\" und als \"rest\" festgelegt werden.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Ein Tupeltyp kann nicht mit einem negativen Wert indiziert werden.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Typassertionsausdrücke sind in der linken Seite von Potenzierungsausdrücken nicht zulässig. Erwägen Sie, den Ausdruck in Klammern zu setzen.", "A_type_literal_property_cannot_have_an_initializer_1247": "Typliteraleigenschaften können keinen Initialisierer aufweisen.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Ein reiner Typenimport kann einen Standardimport oder benannte Bindungen angeben, aber nicht be<PERSON>.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Ein Typprädikat darf nicht auf einen rest-Parameter verweisen.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Ein Typprädikat darf nicht auf ein Element \"{0}\" in einem Bindungsmuster verweisen.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Ein Typprädikat ist nur an der Rückgabetypposition für Funktionen und Methoden zulässig.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Der Typ eines Typprädikats muss dem Typ seines Parameters zugewiesen werden können.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "<PERSON>, auf den in einer ergänzten Signatur verwiesen wird, muss mit „import type“ oder einem Namespaceimport importiert werden, wenn „isolatedModules“ und „emitDecoratorMetadata“ aktiviert sind.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "<PERSON><PERSON> Variable, deren <PERSON><PERSON> ein \"unique symbol\"-<PERSON><PERSON> ist, muss \"const\" sein.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Ein yield-Ausdruck ist nur in einem Generatortext zulässig.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "Auf die abstrakte Methode \"{0}\" in der Klasse \"{1}\" kann nicht über den super-Ausdruck zugegriffen werden.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Abstrakte Methoden können nur in einer abstrakten Klasse verwendet werden.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "Auf die abstrakte Eigenschaft \"{0}\" in der Klasse \"{1}\" kann im Konstruktor nicht zugegriffen werden.", "Accessibility_modifier_already_seen_1028": "Der Zugriffsmodifizierer ist bereits vorhanden.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Zugriffsmethoden sind nur verfügbar, wenn das Ziel ECMAScript 5 oder höher ist.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Beide Accessoren müssen abstrakt oder nicht abstrakt sein.", "Add_0_to_unresolved_variable_90008": "Der nicht aufgelösten Variablen \"{0}.\" hinzufügen", "Add_a_return_statement_95111": "return-Anwei<PERSON>g hinzufügen", "Add_all_missing_async_modifiers_95041": "Alle fehlenden async-Modifizierer hinzufügen", "Add_all_missing_attributes_95168": "Alle fehlenden Attribute hinzufügen", "Add_all_missing_call_parentheses_95068": "Alle fehlenden Klammern in Aufrufen hinzufügen", "Add_all_missing_function_declarations_95157": "Alle fehlenden Funktionsdeklarationen hinzufügen", "Add_all_missing_imports_95064": "Alle fehlenden Importe hinzufügen", "Add_all_missing_members_95022": "Alle fehlenden Member hinzufügen", "Add_all_missing_override_modifiers_95162": "Alle fehlenden override-Modifizierer hinzufügen", "Add_all_missing_properties_95166": "Alle fehlenden Eigenschaften hinzufügen", "Add_all_missing_return_statement_95114": "Alle fehlenden return-Anweisungen hinzufügen", "Add_all_missing_super_calls_95039": "Alle fehlenden super-Aufrufe hinzufügen", "Add_async_modifier_to_containing_function_90029": "Async-Modifizierer zur enthaltenden Funktion hinzufügen", "Add_await_95083": "\"await\" hinz<PERSON><PERSON><PERSON>", "Add_await_to_initializer_for_0_95084": "\"await\" zum Initialisierer für \"{0}\" hinzufügen", "Add_await_to_initializers_95089": "\"await\" zu Initialisierern hinzufügen", "Add_braces_to_arrow_function_95059": "Geschweifte Klammern zu Pfeilfunktion hinzufügen", "Add_const_to_all_unresolved_variables_95082": "\"const\" zu allen nicht aufgelösten Variablen hinzufügen", "Add_const_to_unresolved_variable_95081": "\"const\" zur nicht aufgelösten Variable hinzufügen", "Add_definite_assignment_assertion_to_property_0_95020": "Definitive Zuweisungsassertion zu Eigenschaft \"{0}\" hinzufügen", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Allen nicht initialisierten Eigenschaften definitive Zuweisungsassertionen hinzufügen", "Add_export_to_make_this_file_into_a_module_95097": "\"export {}\" hi<PERSON><PERSON><PERSON><PERSON>, um diese Datei in ein Modul umzu<PERSON>deln", "Add_extends_constraint_2211": "\"extends\"-Einschränkung hinzufügen", "Add_extends_constraint_to_all_type_parameters_2212": "\"extends\"-Einschränkung zu allen Typparametern hinzufügen", "Add_import_from_0_90057": "Import aus \"{0}\" hinzufügen", "Add_index_signature_for_property_0_90017": "Indexsignatur für die Eigenschaft \"{0}\" hinzufügen", "Add_initializer_to_property_0_95019": "Initialisierer zu Eigenschaft \"{0}\" hinzufügen", "Add_initializers_to_all_uninitialized_properties_95027": "Allen nicht initialisierten Eigenschaften Initialisierer hinzufügen", "Add_missing_attributes_95167": "Fehlende Attribute hinzufügen", "Add_missing_call_parentheses_95067": "Fehlende Klammern in Aufrufen hinzufügen", "Add_missing_enum_member_0_95063": "Fehlenden Enumerationsmember \"{0}\" hinzufügen", "Add_missing_function_declaration_0_95156": "Fehlende Funktionsdeklaration \"{0}\" hinzufügen", "Add_missing_new_operator_to_all_calls_95072": "Fe<PERSON>enden new-Operator zu allen Aufrufen hinzufügen", "Add_missing_new_operator_to_call_95071": "Fehlender new-Operator zum Aufruf hinzufügen", "Add_missing_properties_95165": "Fehlende Eigenschaften hinzufügen", "Add_missing_super_call_90001": "Fehlenden super()-<PERSON>f<PERSON><PERSON> hinzufü<PERSON>", "Add_missing_typeof_95052": "Fehlenden \"typeof\" hinzufügen", "Add_names_to_all_parameters_without_names_95073": "Namen zu allen Parametern ohne Namen hinzufügen", "Add_or_remove_braces_in_an_arrow_function_95058": "Geschweifte Klammern zu einer Pfeilfunktion hinzufügen oder daraus entfernen", "Add_override_modifier_95160": "override-Modifizierer hinzufügen", "Add_parameter_name_90034": "Parameternamen hinzufügen", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "<PERSON> nicht aufgelösten Variablen, die einem Membernamen entsprechen, Qualifizierer hinzufügen", "Add_to_all_uncalled_decorators_95044": "Allen nicht aufgerufenen Decorators \"()\" hinzufügen", "Add_ts_ignore_to_all_error_messages_95042": "<PERSON> \"@ts-ignore\" hinzufügen", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Fügen Sie einem Typ „undefined“ hinzu, wenn über einen Index darauf zugegriffen wird.", "Add_undefined_to_optional_property_type_95169": "„Undefined“ zum optionalen Eigenschaftstyp hinzufügen", "Add_undefined_type_to_all_uninitialized_properties_95029": "Allen nicht initialisierten Eigenschaften einen nicht definierten Typ hinzufügen", "Add_undefined_type_to_property_0_95018": "undefined-<PERSON>p zu Eigenschaft \"{0}\" hinzufügen", "Add_unknown_conversion_for_non_overlapping_types_95069": "Konvertierung \"unknown\" für Typen ohne Überschneidung hinzufügen", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "\"unknown\" zu allen Konvertierungen für Typen ohne Überschneidung hinzufügen", "Add_void_to_Promise_resolved_without_a_value_95143": "\"Void\" zu ohne Wert aufgelöstem Promise hinzufügen", "Add_void_to_all_Promises_resolved_without_a_value_95144": "\"Void\" allen ohne Wert aufgelösten Promises hinzufügen", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Das Hinzufügen einer \"tsconfig.json\"-<PERSON><PERSON> erle<PERSON>t die Organisation von Projekten, die sowohl TypeScript- als auch JavaScript-Dateien enthalten. Weitere Informationen finden Sie unter https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Alle Deklarationen von \"{0}\" müssen identische Modifizierer aufweisen.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Alle Deklarationen von \"{0}\" müssen identische Modifizierer aufweisen.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Alle Deklarationen von \"{0}\" müssen identische Typparameter aufweisen.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Alle Deklarationen einer abstrakten Methode müssen aufeinanderfolgend sein.", "All_destructured_elements_are_unused_6198": "Alle destrukturierten Elemente werden nicht verwendet.", "All_imports_in_import_declaration_are_unused_6192": "<PERSON><PERSON> der Importe in der Importdeklaration wird verwendet.", "All_type_parameters_are_unused_6205": "Sämtliche Typparameter werden nicht verwendet.", "All_variables_are_unused_6199": "Alle Variablen werden nicht verwendet.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "<PERSON><PERSON> zu, dass JavaScript-Dateien Teil Ihres Programms werden. Verwenden Sie die Option „checkJS“, um Fehler aus diesen Dateien abzurufen.", "Allow_accessing_UMD_globals_from_modules_6602": "Zugriff auf globale UMD-Bibliotheken aus Modulen zulassen", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Standardimporte von Modulen ohne Standardexport zulassen. Dies wirkt sich nicht auf die Codeausgabe aus, lediglich auf die Typprüfung.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "\"Import x from y\" zulassen, wenn ein Modul keinen Standardexport hat.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Das Importieren von Hilfsfunktionen aus tslib einmal pro Projekt zulassen, anstatt sie pro Datei einzu<PERSON>ziehen.", "Allow_javascript_files_to_be_compiled_6102": "Kompilierung von JavaScript-<PERSON><PERSON> zu<PERSON>.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "<PERSON><PERSON> zu, dass mehrere Ordner beim Auf<PERSON><PERSON><PERSON> von Mo<PERSON>len als ein Ordner behandelt werden.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "Der bereits enthaltene Dateiname \"{0}\" unterscheidet sich vom Dateinamen \"{1}\" nur hinsichtlich der Groß-/Kleinschreibung.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Die Umgebungsmoduldeklaration darf keinen relativen Modulnamen angeben.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Umgebungsmodule dürfen nicht in andere Module oder Namespaces geschachtelt werden.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Ein AMD-Mo<PERSON><PERSON> darf nicht mehrere Namenzuweisungen aufweisen.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Ein abstrakter Accessor kann keine Implementierung aufweisen.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Ein Zugriffsmodifizierer kann nicht mit einem privaten Bezeichner verwendet werden.", "An_accessor_cannot_have_type_parameters_1094": "Eine Zugriffsmethode darf keine Typparameter aufweisen.", "An_accessor_property_cannot_be_declared_optional_1276": "Eine Accessoreigenschaft kann nicht als optional deklariert werden.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Eine Umgebungsmoduldeklaration ist nur auf der obersten Ebene in einer Datei zulässig.", "An_argument_for_0_was_not_provided_6210": "<PERSON><PERSON>r \"{0}\" wurde ein Argument nicht angegeben.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Es wurde kein Argument angegeben, das diesem Bindungsmuster entspricht.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Ein arithmetischer Operand muss vom Typ \"any\", \"number\" oder \"bigint\" oder ein Enumerationstyp sein.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Eine Pfeilfunktion darf keinen this-Parameter aufweisen.", "An_async_function_or_method_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_de_2705": "Eine Async-Funktion oder -Methode in ES5/ES3 er<PERSON><PERSON> den Konstruktur \"Promise\".  <PERSON><PERSON><PERSON>, dass Sie über eine Deklaration für den Konstruktor \"Promise\" verfügen, oder schließen Sie \"ES2015\" in Ihre Option \"--lib\" ein.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Eine asynchrone Funktion oder Methode muss \"Promise\" zurückgeben. <PERSON><PERSON><PERSON>, dass Sie über eine Deklaration für \"Promise\" verfügen, oder schließen Sie ES2015 in Ihrer Option \"--lib\" ein.", "An_async_iterator_must_have_a_next_method_2519": "Ein Async-Iterator muss eine \"next()\"-Async-<PERSON><PERSON> aufweisen.", "An_element_access_expression_should_take_an_argument_1011": "Ein Ausdruck für einen Elementzugriff muss ein Argument verwenden.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Ein Enumerationsmember kann nicht mit einem privaten Bezeichner benannt werden.", "An_enum_member_cannot_have_a_numeric_name_2452": "Ein Enumerationsmember darf keinen numerischen Namen besitzen.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Auf einen Namen eines Enumerationsmembers muss ein \",\", \"=\" oder \"}\" folgen.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Eine erweiterte Version dieser Informationen mit allen möglichen Compileroptionen", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Eine Exportzuweisung darf nicht in einem Modul mit anderen exportierten Elementen verwendet werden.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Eine Exportzuweisung darf nicht in einem Namespace verwendet werden.", "An_export_assignment_cannot_have_modifiers_1120": "Eine Exportzuweisung darf keine Modifizierer besitzen.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Eine Exportzuweisung muss sich auf der obersten Ebene einer Datei- oder Moduldeklaration befinden.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Eine Exportdeklaration kann nur auf der obersten Ebene eines Moduls verwendet werden.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Eine Exportdeklaration kann nur auf der obersten Ebene eines Namespace oder Moduls verwendet werden.", "An_export_declaration_cannot_have_modifiers_1193": "Eine Exportdeklaration darf keine Modifizierer besitzen.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "<PERSON><PERSON>r einen Ausdruck vom Typ \"void\" kann nicht getestet werden, ob er wahr oder falsch ist.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Ein erweiterter Unicode-Escapewert muss zwischen 0x0 und 0x10FFFF (einschließlich) liegen.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Ein Bezeichner oder ein Schlüsselwort kann nicht direkt auf ein numerisches Literal folgen.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Eine Implementierung darf nicht in Umgebungskontexten deklariert werden.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Ein Importalias kann nicht auf eine Deklaration verweisen, die mithilfe von \"export type\" exportiert wurde.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Ein Importalias kann nicht auf eine Deklaration verweisen, die mithilfe von \"import type\" importiert wurde.", "An_import_alias_cannot_use_import_type_1392": "Ein Importalias kann \"import type\" nicht verwenden.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Eine Importdeklaration kann nur auf der obersten Ebene eines Moduls verwendet werden.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Eine Importdeklaration kann nur auf der obersten Ebene eines Namespace oder Moduls verwendet werden.", "An_import_declaration_cannot_have_modifiers_1191": "Eine Importdeklaration darf keine Modifizierer besitzen.", "An_import_path_cannot_end_with_a_0_extension_Consider_importing_1_instead_2691": "Ein Importpfad darf nicht mit einer Erweiterung \"{0}\" enden. Importieren Sie ggf. stattdessen \"{1}\".", "An_index_signature_cannot_have_a_rest_parameter_1017": "Eine Indexsignatur darf keinen rest-Parameter besitzen.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Eine Indexsignatur darf kein nachstehendes Komma aufweisen.", "An_index_signature_must_have_a_type_annotation_1021": "Eine Indexsignatur muss eine Typanmerkung besitzen.", "An_index_signature_must_have_exactly_one_parameter_1096": "Eine Indexsignatur muss genau einen Parameter besitzen.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Ein Indexsignaturparameter darf kein Fragezeichen aufweisen.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Ein Indexsignaturparameter darf keinen Zugriffsmodifizierer besitzen.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Ein Indexsignaturparameter darf keinen Initialisierer besitzen.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Ein Indexsignaturparameter muss eine Typanmerkung besitzen.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Ein Indexsignaturparametertyp darf kein Literaltyp oder generischer Typ sein. Erwägen Sie stattdessen die Verwendung eines zugeordneten Objekttyps.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Ein Parametertyp für die Indexsignatur muss \"string\", \"number\", \"symbol\" oder ein Vorlagenliteraltyp sein.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Auf einen Instanziierungsausdruck kann kein Eigenschaftenzugriff folgen.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "<PERSON>e Schnittstelle kann nur einen Bezeichner/\"qualified-name\" mit optionalen Typargumenten erweitern.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Eine Schnittstelle kann nur einen Objekttyp oder eine Schnittmenge von Objekttypen mit statisch bekannten Membern erweitern.", "An_interface_cannot_extend_a_primitive_type_like_0_an_interface_can_only_extend_named_types_and_clas_2840": "Eine Schnittstelle kann einen primitiven Typ wie „{0}“ nicht erweitern; eine Schnittstelle kann nur benannte Typen und Klassen erweitern", "An_interface_property_cannot_have_an_initializer_1246": "Schnittstelleneigenschaften können keinen Initialisierer aufweisen.", "An_iterator_must_have_a_next_method_2489": "Ein Iterator muss eine Methode \"next()\" besitzen.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "<PERSON><PERSON> Verwendung eines @jsx-Pragmas mit JSX-Fragmenten wird ein @jsxFrag-Pragma benötigt.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Ein Objektliteral darf nicht mehrere get-/set-Zugriffsmethoden mit dem gleichen Namen besitzen.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Ein Objektliteral darf nicht über mehrere Eigenschaften mit demselben Namen verfügen.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Ein Objektliteral darf nicht eine Eigenschaft und eine Zugriffsmethode mit demselben Namen besitzen.", "An_object_member_cannot_be_declared_optional_1162": "Ein Objektmember darf nicht als optional deklariert werden.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Eine optionale Kette kann keine privaten Bezeichner enthalten.", "An_optional_element_cannot_follow_a_rest_element_1266": "Ein optionales Element darf nicht auf ein rest-Element folgen.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Ein äußerer Wert von \"this\" wird durch diesen Container verborgen.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Eine Überladungssignatur darf nicht als ein Generator deklariert werden.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Unäre Ausdrücke mit dem Operator \"{0}\" sind auf der linken Seite von Potenzierungsausdrücken nicht zulässig. Erwägen Sie, den Ausdruck in Klammern zu setzen.", "Annotate_everything_with_types_from_JSDoc_95043": "Alle Funktionen mit Typen aus JSDoc kommentieren", "Annotate_with_type_from_JSDoc_95009": "Mit Typ aus JSDoc kommentieren", "Another_export_default_is_here_2753": "Ein weiterer Exportstandardwert befindet sich hier.", "Are_you_missing_a_semicolon_2734": "Fe<PERSON>t ein Semikolon?", "Argument_expression_expected_1135": "<PERSON>s wurde ein Argumentausdruck erwartet.", "Argument_for_0_option_must_be_Colon_1_6046": "Das Argument für die Option \"{0}\" muss \"{1}\" sein.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "Das Argument des dynamischen Imports kann kein Überfüllungselement sein.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "Das Argument vom Typ \"{0}\" kann dem Parameter vom Typ \"{1}\" nicht zugewiesen werden.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "Das Argument vom Typ „{0}“ kann dem Parameter vom Typ „{1}“ mit „exactOptionalPropertyTypes: true“ nicht zugewiesen werden. Erwägen Sie das Hinzufügen von „undefined“ zu den Typen der Zieleigenschaften.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "<PERSON>s wurden keine Argumente für den rest-Parameter \"{0}\" angegeben.", "Array_element_destructuring_pattern_expected_1181": "Ein Arrayelement-Destrukturierungsmuster wurde erwartet.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "Asser<PERSON><PERSON> erfordern, dass jeder Name im Aufrufziel mit einer expliziten Typanmerkung deklariert wird.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "Asser<PERSON><PERSON> erfordern, dass das Aufrufziel ein Bezeichner oder ein qualifizierter Name ist.", "Asterisk_Slash_expected_1010": "\"*/\" wurde erwartet.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Erweiterungen für den globalen Bereich können nur in externen Modulen oder Umgebungsmoduldeklarationen direkt geschachtelt werden.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Erweiterungen für den globalen Bereich sollten den Modifizierer \"declare\" auf<PERSON>sen, wenn sie nicht bereits in einem Umgebungskontext auftreten.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "In Projekt \"{0}\" ist die automatische Erkennung von Eingaben aktiviert. Es wird ein zusätzlicher Auflösungsdurchlauf für das Modul \"{1}\" unter Verwendung von Cachespeicherort \"{2}\" ausgeführt.", "Await_expression_cannot_be_used_inside_a_class_static_block_18037": "Der \"Await\"-Ausdruck kann nicht innerhalb eines statischen Klassenblocks verwendet werden.", "BUILD_OPTIONS_6919": "BUILDOPTIONEN", "Backwards_Compatibility_6253": "Abwärtskompatibilität", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Basisklassenausdrücke können nicht auf Klassentypparameter verweisen.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "Der Rückgabetyp \"{0}\" des Basiskonstruktors ist kein Objekttyp oder eine Schnittmenge von Objekttypen mit statisch bekannten Membern.", "Base_constructors_must_all_have_the_same_return_type_2510": "Basiskonstruktoren müssen alle den gleichen Rückgabetyp aufweisen.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Das Basisverzeichnis zum Auflösen nicht absoluter Modulnamen.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "bigint-Literale sind nicht verfügbar, wenn die Zielversion niedriger ist als ES2020.", "Binary_digit_expected_1177": "<PERSON><PERSON> wurde eine Binärzahl erwartet.", "Binding_element_0_implicitly_has_an_1_type_7031": "Das Bindungselement \"{0}\" weist implizit einen Typ \"{1}\" auf.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Die blockbezogene Variable \"{0}\" wurde vor ihrer Deklaration verwendet.", "Build_a_composite_project_in_the_working_directory_6925": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein zusammengesetztes Projekt im Arbeitsverzeichnis.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "<PERSON><PERSON><PERSON><PERSON> alle Projekte, einsch<PERSON><PERSON>lich der Projekte, die aktuell zu sein scheinen.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Mindestens ein Projekt und die zugehörigen Abhängigkeiten erstellen, wenn veraltet", "Build_option_0_requires_a_value_of_type_1_5073": "Die Buildoption \"{0}\" erfordert einen Wert vom Typ \"{1}\".", "Building_project_0_6358": "Projekt \"{0}\" wird erstellt...", "COMMAND_LINE_FLAGS_6921": "BEFEHLSZEILENFLAGS", "COMMON_COMMANDS_6916": "ALLGEMEINE BEFEHLE", "COMMON_COMPILER_OPTIONS_6920": "ALLGEMEINE COMPILEROPTIONEN", "Call_decorator_expression_90028": "Decorator-Ausdruck aufrufen", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Die Rückgabetypen \"{0}\" und \"{1}\" der Aufrufsignatur sind nicht kompatibel.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Eine Aufrufsignatur ohne Rückgabetypanmerkung weist implizit einen any-Rückgabetyp auf.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Aufrufsignaturen ohne Argumente weisen inkompatible Rückgabetypen \"{0}\" und \"{1}\" auf.", "Call_target_does_not_contain_any_signatures_2346": "Das Aufrufziel enthält keine Signaturen.", "Can_only_convert_logical_AND_access_chains_95142": "Es können nur Zugriffsketten mit logischem \"Und\" konvertiert werden.", "Can_only_convert_named_export_95164": "Nur ein benannter Export kann konvertiert werden.", "Can_only_convert_property_with_modifier_95137": "Die Eigenschaft kann nur mit einem Modifizierer konvertiert werden.", "Can_only_convert_string_concatenation_95154": "Es ist nur die Konvertierung einer Zeichenfolgenverkettung möglich.", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Der Zugriff auf \"{0}.{1}\" ist nicht möglich, da \"{0}\" ein <PERSON><PERSON> ist, aber kein Namespace. Wollten Sie den Typ der Eigenschaft \"{1}\" in \"{0}\" mit \"{0}[\"{1}\"]\" abrufen?", "Cannot_access_ambient_const_enums_when_the_isolatedModules_flag_is_provided_2748": "Auf umgebende const-Enumerationen kann nicht zugegriffen werden, wenn das Flag \"--isolatedModules\" angegeben wird.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Ein Konstruktortyp \"{0}\" kann nicht einem Konstruktortyp \"{1}\" zugewiesen werden.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Ein abstrakter Konstruktortyp kann nicht einem nicht abstrakten Konstruktortyp zugewiesen werden.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um eine Klasse handelt.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um eine Konstante handelt.", "Cannot_assign_to_0_because_it_is_a_function_2630": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um eine Funktion handelt.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um einen Namespace handelt.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um eine schreibgeschützte Eigenschaft handelt.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um eine Enumeration handelt.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich um einen Import handelt.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Eine Zuweisung zu \"{0}\" ist nicht möglich, weil es sich nicht um eine Variable handelt.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Eine Zuweisung zur private Methode \"{0}\" ist nicht möglich. In private Methoden kann nicht geschrieben werden.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "<PERSON> Modul \"{0}\" kann nicht erweitert werden, weil es in eine Nicht-Modulentität aufgelöst wird.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Das Modul \"{0}\" kann nicht mit Wertexporten vergrößert werden, da es zu einer Entität aufgelöst wird, die kein Modul darstellt.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Module können nur mithilfe der Option \"{0}\" kompiliert werden, wenn die Kennzeichnung \"-module\" den Wert \"amd\" oder \"system\" aufweist.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Eine Instanz der abstrakten Klasse kann nicht erstellt werden.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Die Iteration kann nicht an einen Wert delegiert werden, weil die next-Methode des zugehörigen Iterators den Typ \"{1}\" erwartet, aber der enthaltende Generator immer \"{0}\" sendet.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "\"{0}\" kann nicht exportiert werden. Nur lokale Deklarationen können aus einem Modul exportiert werden.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "Eine Klasse \"{0}\" kann nicht erweitert werden. Der Klassenkonstruktor ist als privat markiert.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "<PERSON><PERSON> Schnittstelle \"{0}\" kann nicht erweitert werden. Meinten Sie \"implements\"?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Im angegebenen Verzeichnis \"{0}\" wurde keine <PERSON> \"tsconfig.json\" gefunden.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Im angegebenen Verzeichnis \"{0}\" wurde keine \"tsconfig.json\"-Datei gefunden.", "Cannot_find_global_type_0_2318": "Der globale Typ \"{0}\" wurde nicht gefunden.", "Cannot_find_global_value_0_2468": "Der globale Wert \"{0}\" wurde nicht gefunden.", "Cannot_find_lib_definition_for_0_2726": "Die Bibliotheksdefinition für \"{0}\" wurde nicht gefunden.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "Die Bibliotheksdefinition für \"{0}\" wurde nicht gefunden. Meinten Sie \"{1}\"?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Das Modul \"{0}\" wurde nicht gefunden. Erwägen Sie die Verwendung von \"--resolveJsonModule\" zum Importieren eines Moduls mit der Erweiterung \".json\".", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_node_or_to_add_aliases_to_th_2792": "Das Modul \"{0}\" wurde nicht gefunden. Möchten Sie die Option \"moduleResolution\" auf \"node\" festlegen oder Aliase zur Option \"paths\" hinzufügen?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "<PERSON> Modul \"{0}\" oder die zugehörigen Typdeklarationen wurden nicht gefunden.", "Cannot_find_name_0_2304": "Der Name \"{0}\" wurde nicht gefunden.", "Cannot_find_name_0_Did_you_mean_1_2552": "Der Name \"{0}\" wurde nicht gefunden. Meinten Sie \"{1}\"?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Der Name \"{0}\" wurde nicht gefunden. Meinten Sie den Instanzmember \"this.{0}\"?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Der Name \"{0}\" wurde nicht gefunden. Meinten Sie den statischen Member \"{1}.{0}\"?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "Der Name „{0}“ wurde nicht gefunden. <PERSON><PERSON><PERSON> Si<PERSON> dies in eine asynchrone Funktion schreiben?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Ihre Zielbibliothek ändern? Ändern Sie die Compileroption \"lib\" in \"{1}\" oder höher.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Ihre Zielbibliothek ändern? Ändern Sie die Compileroption \"lib\" so ab, dass sie \"dom\" enthält.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Typdefinitionen für einen Test Runner installieren? Versuchen Sie es mit \"npm i --save-dev @types/jest\" oder \"npm i --save-dev @types/mocha\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Typdefinitionen für einen Test Runner installieren? Versuchen Sie es mit \"npm i --save-dev @types/jest\" oder \"npm i --save-dev @types/mocha\", und fügen Sie dann dem Typenfeld in Ihrer tsconfig-Datei \"jest\" oder \"mocha\" hinzu.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Typdefinitionen für jQuery installieren? Versuchen Sie es mit \"npm i --save-dev @types/jquery\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Typdefinitionen für jQuery installieren? Versuchen Sie es mit \"npm i --save-dev @types/jquery\", und fügen Sie dann dem Typenfeld in Ihrer tsconfig-Datei \"jquery\" hinzu.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Typdefinitionen für Node installieren? Versuchen Sie es mit \"npm i --save-dev @types/node\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Der Name \"{0}\" wurde nicht gefunden. Müssen Sie Typdefinitionen für Node installieren? Versuchen Sie es mit \"npm i --save-dev @types/node\", und fügen Sie dann dem Typenfeld in Ihrer tsconfig-Datei \"node\" hinzu.", "Cannot_find_namespace_0_2503": "Der Namespace \"{0}\" wurde nicht gefunden.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Namespace \"{0}\" wurde nicht gefunden. Meinten Sie \"{1}\"?", "Cannot_find_parameter_0_1225": "Der Parameter \"{0}\" wurde nicht gefunden.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Das gemeinsame Unterverzeichnis für die Eingabedateien wurde nicht gefunden.", "Cannot_find_type_definition_file_for_0_2688": "Die Typdefinitionsdatei für \"{0}\" wurde nicht gefunden.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Typdeklarationsdateien können nicht importiert werden. Importieren Sie ggf. \"{0}\" anste<PERSON> von \"{1}\".", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Die Variable \"{0}\" mit dem äußeren Bereich im gleichen Bereich wie die Deklaration \"{1}\" mit dem Blockbereich kann nicht initialisiert werden.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Ein Objekt, das möglicherweise NULL ist, kann nicht aufgerufen werden.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Ein Objekt, das möglicherweise NULL oder nicht definiert ist, kann nicht aufgerufen werden.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Ein Objekt, das möglicherweise nicht definiert ist, kann nicht aufgerufen werden.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "Der Wert kann nicht durchlaufen werden, weil die next-Methode des zugehörigen Iterators den Typ \"{1}\" erwartet, die Arraydestrukturierung aber immer \"{0}\" sendet.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "Der Wert kann nicht durchlaufen werden, weil die next-Methode des zugehörigen Iterators den Typ \"{1}\" erwartet, die Arrayverteilung aber immer \"{0}\" sendet.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "Der Wert kann nicht durchlaufen werden, weil die next-Methode des zugehörigen Iterators den Typ \"{1}\" erwartet, \"for-of\" aber immer \"{0}\" sendet.", "Cannot_prepend_project_0_because_it_does_not_have_outFile_set_6308": "Das Projekt \"{0}\" kann nicht vorgestellt werden, weil \"outFile\" nicht festgelegt wurde.", "Cannot_read_file_0_5083": "<PERSON> Datei \"{0}\" kann nicht gelesen werden.", "Cannot_read_file_0_Colon_1_5012": "<PERSON> Datei \"{0}\" kann nicht gelesen werden: {1}", "Cannot_redeclare_block_scoped_variable_0_2451": "Die blockbezogene Variable \"{0}\" Blockbereich kann nicht erneut deklariert werden.", "Cannot_redeclare_exported_variable_0_2323": "Die exportierte Variable \"{0}\" kann nicht erneut deklariert werden.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Der Bezeichner \"{0}\" in der Catch-Klausel kann nicht erneut deklariert werden.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Kann in einer Typanmerkung keinen Funktionsaufruf starten.", "Cannot_update_output_of_project_0_because_there_was_error_reading_file_1_6376": "Die Ausgabe des Projekts \"{0}\" kann nicht aktualisiert werden, weil es beim Lesen der Datei \"{1}\" zu einem Fehler gekommen ist.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "JSX kann nur verwendet werden, wenn das Flag \"-jsx\" angegeben wird.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_the_isolatedModules_flag_is_provided_1269": "„export import“ kann nicht für einen Typ oder einen reinen Typnamespace verwendet werden, wenn das Flag „--isolatedModules“ angegeben wird.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Es können keine imports-, exports- oder module-Erweiterungen verwendet werden, wenn \"-module\" den Wert \"none\" aufweist.", "Cannot_use_namespace_0_as_a_type_2709": "Der Namespace \"{0}\" kann nicht als Typ verwendet werden.", "Cannot_use_namespace_0_as_a_value_2708": "Der Namespace \"{0}\" kann nicht als Wert verwendet werden.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "\"This\" kann nicht in einem statischen Eigenschafteninitialisierer einer ergänzten Klasse verwendet werden.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "Die Datei \"{0}\" kann nicht geschrieben werden, weil hierdurch die TSBUILDINFO-Datei überschrieben wird, die durch das referenzierte Projekt \"{1}\" generiert wird.", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Die Datei \"{0}\" kann nicht geschrieben werden, da sie durch mehrere Eingabedateien überschrieben würde.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "Die Datei \"{0}\" kann nicht geschrieben werden, da sie eine Eingabedatei überschreiben würde.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Die Variable der Catch-<PERSON>el darf keinen Initialisierer aufweisen.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Die Anmerkung des Variablentyps der catch-Klausel muss \"any\" oder \"unknown\" lauten, sofern angegeben.", "Change_0_to_1_90014": "\"{0}\" in \"{1}\" ändern", "Change_all_extended_interfaces_to_implements_95038": "Alle erweiterten Schnittstellen in \"implements\" ändern", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Alle jsdoc-style-Typen in TypeScript ändern", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Alle jsdoc-style-Typen in TypeScript ändern (und Nullable-Typen \"| undefined\" hinzufügen)", "Change_extends_to_implements_90003": "\"extends\" in \"implements\" ändern", "Change_spelling_to_0_90022": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in \"{0}\" ändern", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Suchen Sie nach Klasseneigenschaften, die im Konstruktor deklariert, aber nicht festgelegt sind.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "Überprüfen Sie, ob die Argumente für die Methoden „bind“, „call“ und „apply“ mit der ursprünglichen Funktion übereinstimmen.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "<PERSON>s wird überprüft, ob \"{0}\" das längste übereinstimmende Präfix für \"{1}\"–\"{2}\" ist.", "Circular_definition_of_import_alias_0_2303": "Zirkuläre Definition des Importalias \"{0}\".", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Eine Zirkularität wurde beim Auflösen der Konfiguration erkannt: {0}", "Circularity_originates_in_type_at_this_location_2751": "Die Zirkularität stammt vom Typ an diesem Standort.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "Die Klasse \"{0}\" definiert die Instanzmember-Zugriffsmethode \"{1}\", die erweiterte Klasse \"{2}\" definiert diesen jedoch als Instanzmemberfunktion.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "Die Klasse \"{0}\" definiert die Instanzmember-Zugriffsmethode \"{1}\", die erweiterte Klasse \"{2}\" definiert diese jedoch als Instanzmember-Zugriffsmethode.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "Die Klasse \"{0}\" definiert die Instanzmembereigenschaft \"{1}\", die erweiterte Klasse \"{2}\" definiert diese jedoch als Instanzmemberfunktion.", "Class_0_incorrectly_extends_base_class_1_2415": "Die Klasse \"{0}\" erweitert fälschlicherweise die Basisklasse \"{1}\".", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "Die Klasse \"{0}\" implementiert fälschlicherweise die Klasse \"{1}\". Wollten Sie \"{1}\" erweitern und ihre Member als Unterklasse vererben?", "Class_0_incorrectly_implements_interface_1_2420": "Die Klasse \"{0}\" implementiert fälschlicherweise die Schnittstelle \"{1}\".", "Class_0_used_before_its_declaration_2449": "Klasse \"{0}\", die vor der Deklaration verwendet wurde.", "Class_constructor_may_not_be_a_generator_1368": "Der Klassenkonstruktor darf kein Generator sein.", "Class_constructor_may_not_be_an_accessor_1341": "Der Klassenkonstruktor darf kein <PERSON> sein.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "Die Klassendeklaration kann die Überladungsliste für \"{0}\" nicht implementieren.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "Klassendeklarationen dürfen maximal ein \"@augments\"- oder \"@extends\"-<PERSON> aufweisen.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Decorator-<PERSON>ement<PERSON> von Klassen können nicht mit einem statischen privaten Bezeichner verwendet werden. Erwägen Si<PERSON>, das experimentelle Decorator-Element zu entfernen.", "Class_name_cannot_be_0_2414": "Der Klassenname darf nicht \"{0}\" sein.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "Der Klassenname darf nicht \"Object\" lauten, wenn ES5 mit Modul \"{0}\" als Ziel verwendet wird.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Die statische Seite der Klasse \"{0}\" erweitert fälschlicherweise die statische Seite der Basisklasse \"{1}\".", "Classes_can_only_extend_a_single_class_1174": "Klassen dürfen nur eine einzelne Klasse erweitern.", "Classes_may_not_have_a_field_named_constructor_18006": "Klassen dürfen kein Feld mit dem Namen \"constructor\" auf<PERSON><PERSON>.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "Code, der in einer Klasse enthalten ist, wird im Strict-Modus von JavaScript ausgewertet, der diese Verwendung von \"{0}\" nicht zulässt. Weitere Informationen finden Sie unter https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Befehlszeilenoptionen", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Kompilieren Sie das dem Pfad zugewiesene Projekt zu dessen Konfigurationsdatei oder zu einem Ordner mit der Datei \"tsconfig.json\".", "Compiler_Diagnostics_6251": "Compilerdiagnose", "Compiler_option_0_expects_an_argument_6044": "Die Compileroption \"{0}\" erwartet ein Argument.", "Compiler_option_0_may_not_be_used_with_build_5094": "Die Compileroption \"--{0}\" darf nicht mit \"--build\" verwendet werden.", "Compiler_option_0_may_only_be_used_with_build_5093": "Die Compileroption \"--{0}\" darf nur mit \"--build\" verwendet werden.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "Die Compileroption „{0}“ des Werts „{1}“ ist instabil. Verwenden Sie „Nightly TypeScript“, um diesen Fehler zu beheben. Versuchen Sie die Aktualisierung mit „npm install -D typescript@next“ durchzuführen.", "Compiler_option_0_requires_a_value_of_type_1_5024": "Die Compileroption \"{0}\" erfordert einen Wert vom Typ \"{1}\".", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Der Compiler reserviert den Namen \"{0}\", wenn er einen privaten Bezeichner für Vorgängerversionen ausgibt.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Kompiliert das sich am angegebenen Pfad befindliche TypeScript-Projekt.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Kompiliert das aktuelle Projekt (tsconfig.json im Arbeitsverzeichnis.)", "Compiles_the_current_project_with_additional_settings_6929": "Kompiliert das aktuelle Projekt mit zusätzlichen Einstellungen.", "Completeness_6257": "Vollständigkeit", "Composite_projects_may_not_disable_declaration_emit_6304": "In zusammengesetzten Projekten kann die Deklarationsausgabe nicht deaktiviert werden.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Zusammengesetzte Projekte dürfen die inkrementelle Kompilierung nicht deaktivieren.", "Computed_from_the_list_of_input_files_6911": "Aus der Liste der Eingabedateien berechnet", "Computed_property_names_are_not_allowed_in_enums_1164": "Berechnete Eigenschaftennamen sind in Enumerationen unzulässig.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Berechnete Werte sind in einer Enumeration mit Membern mit Zeichenfolgenwerten nicht zulässig.", "Concatenate_and_emit_output_to_single_file_6001": "Verketten und Ausgabe in einer Datei speichern.", "Conflicting_definitions_for_0_found_at_1_and_2_Consider_installing_a_specific_version_of_this_librar_4090": "In Konflikt stehende Definitionen für \"{0}\" wurden unter \"{1}\" und \"{2}\" gefunden. Installieren Sie ggf. eine bestimmte Version dieser Bibliothek, um den Konflikt aufzulösen.", "Conflicts_are_in_this_file_6201": "In dieser Datei liegen Konflikte vor.", "Consider_adding_a_declare_modifier_to_this_class_6506": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dieser Klasse einen declare-Modifizierer hinzuzufügen.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Die Rückgabetypen \"{0}\" und \"{1}\" der Konstruktsignatur sind nicht kompatibel.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Eine Konstruktsignatur ohne Rückgabetypanmerkung weist implizit einen any-Rückgabetyp auf.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Konstruktsignaturen ohne Argumente weisen inkompatible Rückgabetypen \"{0}\" und \"{1}\" auf.", "Constructor_implementation_is_missing_2390": "Die Konstruktorimplementierung fehlt.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Der Konstruktor der Klasse \"{0}\" ist privat. Auf ihn kann nur innerhalb der Klassendeklaration zugegriffen werden.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Der Konstruktor der Klasse \"{0}\" ist geschützt. Auf ihn kann nur innerhalb der Klassendeklaration zugegriffen werden.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "Die Typnotation des Konstruktors muss in Klammern gesetzt werden, wenn sie in einem Union-Typ verwendet wird.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "Die Typnotation des Konstruktors muss in Klammern gesetzt werden, wenn sie in einem Intersection-Typ verwendet wird.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Konstruktoren für abgeleitete Klassen müssen einen Aufruf \"super\" enthalten.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Die enthaltene Datei wird nicht angegeben, und das Stammverzeichnis kann nicht ermittelt werden. Die Suche im Ordner \"node_modules\" wird übersprungen.", "Containing_function_is_not_an_arrow_function_95128": "Die enthaltende Funktion ist keine Pfeilfunktion.", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "<PERSON><PERSON><PERSON> Si<PERSON>, welche Methode zum Erkennen von JS-Dateien im Modulformat verwendet wird.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "Die Konvertierung des Typs \"{0}\" in den Typ \"{1}\" kann ein <PERSON> sein, weil die Typen keine ausreichende Überschneidung aufweisen. <PERSON><PERSON> dies beabsichtigt war, konvertieren Sie den Ausdruck zuerst in \"unknown\".", "Convert_0_to_1_in_0_95003": "\"{0}\" in \"{1} in {0}\" konvertieren", "Convert_0_to_mapped_object_type_95055": "\"{0}\" in zugeordneten Objekttyp konvertieren", "Convert_all_const_to_let_95102": "Alle \"const\" in \"let\" konvertieren", "Convert_all_constructor_functions_to_classes_95045": "Alle Konstruktorfunktionen in Klassen konvertieren", "Convert_all_imports_not_used_as_a_value_to_type_only_imports_1374": "Alle nicht als Wert verwendeten Importe in reine Typenimporte konvertieren", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Alle ungültigen Zeichen in HTML-Entitätscode konvertieren", "Convert_all_re_exported_types_to_type_only_exports_1365": "Alle erneut exportierten Typen in reine Typenexporte konvertieren", "Convert_all_require_to_import_95048": "Alle Aufrufe von \"require\" in \"import\" konvertieren", "Convert_all_to_async_functions_95066": "Alle in asynchrone Funktionen konvertieren", "Convert_all_to_bigint_numeric_literals_95092": "Alle in numerische bigint-Literale konvertieren", "Convert_all_to_default_imports_95035": "Alle in Standardimporte konvertieren", "Convert_all_type_literals_to_mapped_type_95021": "Alle Typliterale in einen zugeordneten Typ konvertieren", "Convert_arrow_function_or_function_expression_95122": "Pfeilfunktion oder Funktionsausdruck konvertieren", "Convert_const_to_let_95093": "\"const\" in \"let\" konvertieren", "Convert_default_export_to_named_export_95061": "Standardexport in benannten Export konvertieren", "Convert_function_declaration_0_to_arrow_function_95106": "Funktionsdeklaration \"{0}\" in Pfeilfunktion konvertieren", "Convert_function_expression_0_to_arrow_function_95105": "Funktionsausdruck \"{0}\" in Pfeilfunktion konvertieren", "Convert_function_to_an_ES2015_class_95001": "Funktion in eine ES2015-Klasse konvertieren", "Convert_invalid_character_to_its_html_entity_code_95100": "Ungültiges Zeichen in entsprechenden HTML-Entitätscode konvertieren", "Convert_named_export_to_default_export_95062": "Benannten Export in Standardexport konvertieren", "Convert_named_imports_to_default_import_95170": "Konvertieren benannter Importe in Standardimporte", "Convert_named_imports_to_namespace_import_95057": "Benannte Importe in Namespaceimport konvertieren", "Convert_namespace_import_to_named_imports_95056": "Namespaceimport in benannte Importe konvertieren", "Convert_overload_list_to_single_signature_95118": "Überladungsliste in einzelne Signatur konvertieren", "Convert_parameters_to_destructured_object_95075": "Parameter in destrukturiertes Objekt konvertieren", "Convert_require_to_import_95047": "\"require\" in \"import\" konvertieren", "Convert_to_ES_module_95017": "In ES-Modul konvertieren", "Convert_to_a_bigint_numeric_literal_95091": "In numerisches bigint-Literal konvertieren", "Convert_to_anonymous_function_95123": "In anonyme Funktion konvertieren", "Convert_to_arrow_function_95125": "In Pfeilfunktion konvertieren", "Convert_to_async_function_95065": "In asynchrone Funktion konvertieren", "Convert_to_default_import_95013": "In Standardimport konvertieren", "Convert_to_named_function_95124": "In benannte Funktion konvertieren", "Convert_to_optional_chain_expression_95139": "In optionalen Kettenausdruck konvertieren", "Convert_to_template_string_95096": "In Vorlagenzeichenfolge konvertieren", "Convert_to_type_only_export_1364": "In reinen Typenexport konvertieren", "Convert_to_type_only_import_1373": "In reinen Typenimport konvertieren", "Corrupted_locale_file_0_6051": "Die Gebietsschemadatei \"{0}\" ist beschädigt.", "Could_not_convert_to_anonymous_function_95153": "Die Konvertierung in eine anonyme Funktion ist nicht möglich.", "Could_not_convert_to_arrow_function_95151": "Die Konvertierung in eine Pfeilfunktion ist nicht möglich.", "Could_not_convert_to_named_function_95152": "Die Konvertierung in eine benannte Funktion ist nicht möglich.", "Could_not_determine_function_return_type_95150": "Der Rückgabetyp der Funktion konnte nicht bestimmt werden.", "Could_not_find_a_containing_arrow_function_95127": "Es wurde keine enthaltende Pfeilfunktion gefunden.", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "<PERSON>s wurde keine Deklarationsdatei für das Modul \"{0}\" gefunden. \"{1}\" weist implizit den Typ \"any\" auf.", "Could_not_find_convertible_access_expression_95140": "<PERSON><PERSON> konvertierbarer Zugriffsausdruck gefunden", "Could_not_find_export_statement_95129": "Die Exportanweisung wurde nicht gefunden.", "Could_not_find_import_clause_95131": "Die Importklausel wurde nicht gefunden.", "Could_not_find_matching_access_expressions_95141": "<PERSON>ine übereinstimmenden Zugriffsausdrücke gefunden", "Could_not_find_name_0_Did_you_mean_1_2570": "Der Name \"{0}\" wurde nicht gefunden. Meinten Sie \"{1}\"?", "Could_not_find_namespace_import_or_named_imports_95132": "Der Namespaceimport oder benannte Importe wurden nicht gefunden.", "Could_not_find_property_for_which_to_generate_accessor_95135": "Die Eigenschaft, für die die Zugriffsmethode generiert werden soll, wurde nicht gefunden.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Der Pfad \"{0}\" mit den Erweiterungen konnte nicht aufgelöst werden: {1}.", "Could_not_write_file_0_Colon_1_5033": "<PERSON> Datei \"{0}\" konnte nicht geschrieben werden. {1}.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "<PERSON>rstellen Sie Quellzuordnungsdateien für ausgegebene JavaScript-Dateien.", "Create_sourcemaps_for_d_ts_files_6614": "<PERSON><PERSON><PERSON><PERSON> Sie Quellzuordnungen für d.ts-Dateien.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Erst<PERSON>t eine tsconfig.json mit den empfohlenen Einstellungen im Arbeitsverzeichnis.", "DIRECTORY_6038": "VERZEICHNIS", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "Die Deklaration erweitert die Deklaration in einer anderen Datei. Dieser Vorgang kann nicht serialisiert werden.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "Zur Deklarationsausgabe für diese Datei muss der private Name \"{0}\" verwendet werden. Eine explizite Typanmerkung kann die Deklarationsausgabe freigeben.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "Zur Deklarationsausgabe für diese Datei muss der private Name \"{0}\" aus dem Modul \"{1}\" verwendet werden. Eine explizite Typanmerkung kann die Deklarationsausgabe freigeben.", "Declaration_expected_1146": "<PERSON>s wurde eine Deklaration erwartet.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Der Deklarationsname steht in Konflikt mit dem integrierten globalen Bezeichner \"{0}\".", "Declaration_or_statement_expected_1128": "<PERSON>s wurde eine Deklaration oder Anweisung erwartet.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Deklaration oder Anweisung erwartet. Dieses Gleichheitszeichen (=) folgt auf einen Anweisungsblock. Wenn Si<PERSON> daher eine Destrukturierungszuweisung schreiben möchten, müssen Sie möglicherweise die gesamte Zuweisung in runde Klammern einschließen.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Deklarationen mit definitiven Zuweisungsassertionen müssen auch Typanmerkungen aufweisen.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Deklarationen mit Initialisierern dürfen keine definitiven Zuweisungsassertionen aufweisen.", "Declare_a_private_field_named_0_90053": "Deklarieren Sie ein privates Feld mit dem Namen \"{0}\".", "Declare_method_0_90023": "Methode \"{0}\" de<PERSON><PERSON><PERSON>", "Declare_private_method_0_90038": "Private Methode \"{0}\" deklarieren", "Declare_private_property_0_90035": "Private Eigenschaft \"{0}\" deklarieren", "Declare_property_0_90016": "Eigenschaft \"{0}\" deklarieren", "Declare_static_method_0_90024": "Statische Methode \"{0}\" deklarieren", "Declare_static_property_0_90027": "Statische Eigenschaft \"{0}\" deklarieren", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "Der Rückgabetyp der Decorator-Funktion „{0}“ kann dem Typ „{1}“ nicht zugewiesen werden.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Der Rückgabetyp der Decorator-Funktion ist „{0}“, es wird jedoch erwartet, dass er „void“ oder „any“ ist.", "Decorators_are_not_valid_here_1206": "Decorators sind hier ungültig.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Decorators dürfen nicht auf mehrere get-/set-Zugriffsmethoden mit dem gleichen Namen angewendet werden.", "Decorators_may_not_be_applied_to_this_parameters_1433": "Decorators dürfen nicht auf this-Parameter angewendet werden.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Vor dem Namen und allen Schlüsselwörtern von Eigenschaftendeklarationen müssen Decorator-Elemente stehen.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "<PERSON>ellen Sie die Variablen der Catch-Klauseln standardmäßig als „unknown“ anstelle von „any“ ein.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "Der Standardexport des Moduls besitzt oder verwendet den privaten Namen \"{0}\".", "Default_library_1424": "Standardbibliothek", "Default_library_for_target_0_1425": "Standardbibliothek für Ziel \"{0}\"", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Definitionen der folgenden Bezeichner stehen in Konflikt mit denen in einer anderen Datei: {0}", "Delete_all_unused_declarations_95024": "Alle nicht verwendeten Deklarationen löschen", "Delete_all_unused_imports_95147": "Alle nicht verwendeten Importe löschen", "Delete_all_unused_param_tags_95172": "Alle nicht verwendeten \"@param\"-Tags löschen", "Delete_the_outputs_of_all_projects_6365": "Löschen Sie die Ausgaben aller Projekte.", "Delete_unused_param_tag_0_95171": "Nicht verwendete \"@param\"-Tag-\"{0}\" löschen", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[<PERSON><PERSON><PERSON>] Verwenden Sie stattdessen \"--jsxFactory\". Geben Sie das Objekt an, das für \"createElement\" aufgerufen wurde, wenn das Ziel die JSX-Ausgabe \"react\" ist.", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[<PERSON><PERSON><PERSON>] Verwenden Sie stattdessen \"--outFile\". Verketten und Ausgeben in eine einzige Datei", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[<PERSON><PERSON><PERSON>] Verwenden Sie stattdessen \"--skipLibCheck\". Überspringen Sie die Typüberprüfung der Standardbibliothek-Deklarationsdateien.", "Deprecated_setting_Use_outFile_instead_6677": "Veraltete Einstellung. Verwenden Sie stattdessen „outFile“.", "Did_you_forget_to_use_await_2773": "<PERSON><PERSON>, \"await\" zu verwenden?", "Did_you_mean_0_1369": "<PERSON><PERSON><PERSON> Si<PERSON> \"{0}\"?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "<PERSON><PERSON><PERSON> Si<PERSON> \"{0}\" auf den <PERSON> \"new (...args: any[]) => {1}\" einschränken?", "Did_you_mean_to_call_this_expression_6212": "Wollten Sie diesen Ausdruck aufrufen?", "Did_you_mean_to_mark_this_function_as_async_1356": "Woll<PERSON> Sie diese Funktion als \"async\" markieren?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "Wollten Sie \":\" verwenden? Ein \"=\" kann nur dann auf einen Eigenschaftennamen folgen, wenn das enthaltende Objektliteral Teil eines Destrukturierungsmusters ist.", "Did_you_mean_to_use_new_with_this_expression_6213": "<PERSON><PERSON><PERSON> Si<PERSON> \"new\" mit diesem Ausdruck verwenden?", "Digit_expected_1124": "<PERSON><PERSON> wurde erwartet.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "Das Verzeichnis \"{0}\" ist nicht vorhanden, Suchvorgänge darin werden übersprungen.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "Das Verzeichnis \"{0}\" enthält keinen package.json-Bereich. Importe werden nicht aufgelöst.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Deaktivieren Sie das Hinzufügen von \"Use Strict\"-Direktiven in ausgesendeten JavaScript-Dateien.", "Disable_checking_for_this_file_90018": "Überprüfung für diese Datei deaktivieren", "Disable_emitting_comments_6688": "Deaktivieren Sie das Ausgeben von Kommentaren.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Deaktivieren Sie das Ausgeben von Deklarationen mit „@internal“ in ihren JSDoc-Kommentaren.", "Disable_emitting_files_from_a_compilation_6660": "Deaktivieren Sie das Ausgeben von Dateien aus einer Kompilierung.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Deaktivieren Sie das Ausgeben von Date<PERSON>, wenn Typüberprüfungsfehler gemeldet werden.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Deaktivieren Sie das Löschen von „const enum“-Deklarationen in generiertem Code.", "Disable_error_reporting_for_unreachable_code_6603": "Deaktivieren Sie die Fehlerberichterstattung für nicht erreichbaren Code.", "Disable_error_reporting_for_unused_labels_6604": "Deaktivieren Sie die Fehlerberichterstattung für nicht verwendete Bezeichnungen.", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Deaktivieren Sie das Generieren von benutzerdefinierten Hilfsfunktionen wie „__extends“ in der kompilierten Ausgabe.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Deaktivieren Sie das Einschließen von Bibliotheksdateien, einschließlich der Standarddatei \"lib.d.ts\".", "Disable_loading_referenced_projects_6235": "Deaktivieren Sie das Laden referenzierter Projekte.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Deaktivieren Sie bevorzugte Quelldateien anstelle von Deklarationsdateien, wenn Si<PERSON> auf zusammengesetzte Projekte verweisen.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Deaktivieren Sie die Meldung übermäßiger Eigenschaftsfehler während der Erstellung von Objektliteralen.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Deaktivieren Sie das Auflösen von symlinks in ihren Realpfad. Dies korreliert mit derselben Kennzeichnung im Knoten.", "Disable_size_limitations_on_JavaScript_projects_6162": "Größenbeschränkungen für JavaScript-Projekte deaktivieren.", "Disable_solution_searching_for_this_project_6224": "Deaktivieren Sie die Projektmappensuche für dieses Projekt.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Deaktivieren Sie die strenge Überprüfung generischer Signaturen in Funktionstypen.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Typ-Akquisition für JavaScript-Projekte deaktivieren", "Disable_truncating_types_in_error_messages_6663": "Deaktivieren Sie das Abschneiden von Typen in Fehlermeldungen.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Deaktivieren Sie die Verwendung von Quelldateien anstelle von Deklarationsdateien aus referenzierten Projekten.", "Disable_wiping_the_console_in_watch_mode_6684": "Deaktivieren Sie das Zurücksetzen der Konsole im Überwachungsmodus.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Deaktiviert den Rückschluss für den Typabruf, indem Dateinamen in einem Projekt betrachtet werden.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Hiermit wird verhin<PERSON>t, dass „import“, „require“ oder „<reference>“ die Anzahl der Dateien erweitern, die TypeScript einem Projekt hinzufügen soll.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Verweise mit uneinheitlicher Groß-/Kleinschreibung auf die gleiche Datei nicht zulassen.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Fügen Sie keine Verweise mit dreifachen Schrägstrichen oder importierte Module zur Liste kompilierter Dateien hinzu.", "Do_not_emit_comments_to_output_6009": "Kommentare nicht an die Ausgabe ausgeben.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "Deklarationen für Code mit einer Anmerkung \"@internal\" nicht ausgeben.", "Do_not_emit_outputs_6010": "<PERSON>ine Ausgaben ausgeben.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "<PERSON><PERSON> Ausgaben ausgeben, wenn Fehler gemeldet wurden.", "Do_not_emit_use_strict_directives_in_module_output_6112": "<PERSON><PERSON> \"use strict\"-Direktiven in Modulausgabe ausgeben.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "const-Enumerationsdeklarationen im generierten Code nicht löschen.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "<PERSON><PERSON><PERSON>n Sie keine benutzerdefinierten Hilfsfunktionen wie \"__extends\" in der kompilierten Ausgabe.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Beziehen Sie die Standardbibliotheksdatei (lib.d.ts) nicht ein.", "Do_not_report_errors_on_unreachable_code_6077": "<PERSON><PERSON> zu nicht erreichbarem Code nicht melden.", "Do_not_report_errors_on_unused_labels_6074": "<PERSON><PERSON> zu nicht verwendeten Bezeichnungen nicht melden.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Tatsächlichen Pfad von symbolischen Verknüpfungen nicht auflösen.", "Do_not_truncate_error_messages_6165": "K<PERSON>rzen Sie keine Fehlermeldungen.", "Duplicate_function_implementation_2393": "Doppelte Funktionsimplementierung.", "Duplicate_identifier_0_2300": "Do<PERSON>ter Bezeichner \"{0}\".", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Doppelter Bezeichner \"{0}\". Der Compiler reserviert den Namen \"{1}\" im Bereich der obersten Ebene eines Moduls.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Doppelter Bezeichner \"{0}\". Der Compiler reserviert den Namen \"{1}\" im Bereich der obersten Ebene eines Moduls, das asynchrone Funktionen enthält.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "<PERSON><PERSON><PERSON> Bezeichner „{0}“. Der Compiler reserviert den Namen „{1}“ beim Ausgeben von „Super“-Verweisen in statischen Initialisierern.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Doppelter Bezeichner \"{0}\". Der Compiler verwendet die Deklaration \"{1}\", um asynchrone Funktionen zu unterstützen.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "<PERSON><PERSON>ter Bezeichner \"{0}\". Statische Elemente und Instanzelemente dürfen nicht denselben privaten Namen aufweisen.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "<PERSON><PERSON><PERSON> Beze<PERSON> \"arguments\". Der Compiler verwendet \"arguments\" zum Initialisieren der rest-Parameter.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Doppelter Bezeichner \"_newTarget\". Der Compiler verwendet die Variablendeklaration \"_newTarget\" zum Erfassen der Metaeigenschaftenreferenz \"new.target\".", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Doppelter Bezeichner \"_this\". Der Compiler verwendet die Variablendeklaration \"_this\" zum Erfassen des this-Verweises.", "Duplicate_index_signature_for_type_0_2374": "Doppelte Indexsignatur für Typ \"{0}\".", "Duplicate_label_0_1114": "Do<PERSON>te Bezeichnung \"{0}\".", "Duplicate_property_0_2718": "Doppelte Eigenschaft: {0}", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Der Spezifizierer des dynamischen Imports muss den Typ \"string\" auf<PERSON><PERSON>, hier ist er jedoch vom Typ \"{0}\".", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Dynamische Importe werden nur unterstützt, wenn das Flag „--module“ auf „es2020“, „es2022“, „esnext“, „commonjs“, „amd“, „system“, „umd“, „node16“ oder „ Knotenext'.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_assertion_as_arguments_1450": "Dynamische Importe können nur einen Modulspezifizierer und eine optionale Assertion als Argumente akzeptieren.", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_or_nod_1324": "Dynamische Importe unterstützen nur ein zweites Argument, wenn die Option „--module“ auf „esnext“, „node16“ oder „nodenext“ gesetzt ist.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Jeder Member des union-Typs \"{0}\" weist Konstruktsignaturen auf, aber keine dieser Signaturen ist miteinander kompatibel.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Jeder Member des union-Typs \"{0}\" weist Signaturen auf, aber keine dieser Signaturen ist miteinander kompatibel.", "Editor_Support_6249": "Editor-Unterstützung", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "Das Element weist implizit einen Typ \"any\" auf, weil der Ausdruck vom Typ \"{0}\" nicht für den Indextyp \"{1}\" verwendet werden kann.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "Das Element weist implizit einen Typ \"any\" auf, weil der Indexausdruck nicht vom Typ \"number\" ist.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "Das Element weist implizit einen <PERSON>p \"any\" auf, weil der Typ \"{0}\" keine Indexsignatur umfasst.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "Das Element weist implizit einen <PERSON> \"any\" auf, weil der Typ \"{0}\" keine Indexsignatur umfasst. Wollten Sie \"{1}\" aufrufen?", "Emit_6246": "Ausgeben", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Geben Sie ECMAScript-standardkonforme Klassenfelder aus.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "<PERSON><PERSON><PERSON> Si<PERSON> zu Beginn der Ausgabedateien eine UTF-8-Bytereihenfolge-Marke (BOM) aus.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "<PERSON><PERSON><PERSON> Si<PERSON> eine einzelne Datei mit Quellzuordnungen anstelle einer separaten Datei aus.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Geben Sie ein v8-CPU-Profil der Compilerausführung zum Debuggen aus.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Geben Sie zusätzliches JavaScript aus, um die Unterstützung beim Importieren von CommonJS-Modulen zu vereinfachen. Dadurch wird „allowSyntheticDefaultImports“ für die Typkompatibilität aktiviert.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Geben Sie Klassenfelder mit \"Define\" anstelle von \"Set\" aus.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Geben Sie Entwurfstypmetadaten für ergänzte Deklarationen in Quelldateien aus.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "<PERSON><PERSON><PERSON> Sie mehr kompatibles, aber ausführliches und weniger leistungsfähiges JavaScript für die Iteration aus.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "<PERSON><PERSON><PERSON> Sie die Quelle zusammen mit den Quellzuordnungen innerhalb einer einzelnen Datei aus; hierfür muss \"--inlineSourceMap\" oder \"--sourceMap\" festgelegt sein.", "Enable_all_strict_type_checking_options_6180": "Aktivieren Sie alle strengen Typüberprüfungsoptionen.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Aktivieren Sie Farbe und Formatierung in der TypeScript-Ausgabe, um Compilerfehler leichter zu lesen.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Aktivieren Sie Einschränkungen, die die Verwendung eines TypeScript-Projekts mit Projektverweisen ermöglichen.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Aktivieren Sie die Fehlerberichterstattung für Codepfade, die nicht explizit in einer Funktion zurückgegeben werden.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Aktivieren Sie die Fehlerberichterstattung für Ausdrücke und Deklarationen mit einem impliziten „any“-Typ.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Aktivieren Sie die Fehlerberichterstellung für Fallthroughfälle in Switch-Anweisungen.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Aktivieren Sie die Fehlerberichterstattung in typgeprüften JavaScript-Dateien.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Aktivieren Sie die Fehlerberichterstattung, wenn lokale Variablen nicht gelesen werden.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Aktivieren Sie die Fehlerberichterstattung, wenn „this“ den Typ „any“ erhält.", "Enable_experimental_support_for_TC39_stage_2_draft_decorators_6630": "Aktivieren Sie experimentelle Unterstützung für Entwurf-Decorator der TC39-Phase 2.", "Enable_importing_json_files_6689": "Aktivieren Sie das Importieren von JSON-Dateien.", "Enable_project_compilation_6302": "Projektkompilierung aktivieren", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Aktivieren Sie die strict-Methoden \"bind\", \"call\" und \"apply\" für Funktionen.", "Enable_strict_checking_of_function_types_6186": "Aktivieren Sie die strenge Überprüfung für Funktionstypen.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Aktivieren Sie die strenge Überprüfung der Eigenschafteninitialisierung in Klassen.", "Enable_strict_null_checks_6113": "Strenge NULL-Überprüfungen aktivieren.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Aktivieren Sie die Option \"experimentalDecorators\" in Ihrer Konfigurationsdatei.", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Aktivieren Sie das Flag \"--jsx\" in Ihrer Konfigurationsdatei.", "Enable_tracing_of_the_name_resolution_process_6085": "Ablaufverfolgung des Namensauflösungsvorgangs aktivieren.", "Enable_verbose_logging_6713": "Aktivieren Sie die ausführliche Protokollierung.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Ermöglicht Ausgabeinteroperabilität zwischen CommonJS- und ES-Modulen durch die Erstellung von Namespaceobjekten für alle Importe. Impliziert \"AllowSyntheticDefaultImports\".", "Enables_experimental_support_for_ES7_decorators_6065": "Ermöglicht experimentelle Unterstützung für asynchrone ES7-Decorators.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Ermöglicht experimentelle Unterstützung zum Ausgeben von Typmetadaten für Decorators.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "<PERSON><PERSON><PERSON>t die Verwendung indizierter Accessoren für Schlüssel, die mithilfe eines indizierten Typs deklariert wurden.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "<PERSON><PERSON><PERSON>, dass überschreibende Member in abgeleiteten Klassen mit einem Überschreibungsmodifizierer markiert sind.", "Ensure_that_casing_is_correct_in_imports_6637": "<PERSON><PERSON><PERSON>, dass die G<PERSON>ß-/Kleinschreibung beim Import korrekt ist.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "<PERSON><PERSON><PERSON>, dass jede <PERSON> sicher transpiliert werden kann, ohne dass andere Importe erforderlich sind.", "Ensure_use_strict_is_always_emitted_6605": "<PERSON><PERSON><PERSON>, dass \"Use Strict\" immer ausgegeben wird.", "Entry_point_for_implicit_type_library_0_1420": "Einstiegspunkt für implizite Typbibliothek \"{0}\"", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Einstiegspunkt für die implizite Typbibliothek \"{0}\" mit packageId \"{1}\"", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Der in \"compilerOptions\" angegebene Einstiegspunkt der Typbibliothek \"{0}\"", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Der in \"compilerOptions\" angegebene Einstiegspunkt der Typbibliothek \"{0}\" mit packageId \"{1}\"", "Enum_0_used_before_its_declaration_2450": "Enumeration \"{0}\", die vor der Deklaration wurde.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Enumerationsdeklarationen können nur mit Namespace- oder anderen Enumerationsdeklarationen zusammengeführt werden.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Enumerationsdeklarationen müssen alle konstant oder nicht konstant sein.", "Enum_member_expected_1132": "Ein Enumerationsmember wurde erwartet.", "Enum_member_must_have_initializer_1061": "Ein Enumerationsmember muss einen Initialisierer aufweisen.", "Enum_name_cannot_be_0_2431": "Der Enumerationsname darf nicht \"{0}\" sein.", "Enum_type_0_has_members_with_initializers_that_are_not_literals_2535": "Der Enumerationstyp \"{0}\" weist Member mit Initialisierern auf, die keine Literale sind.", "Errors_Files_6041": "Fehlerdateien", "Examples_Colon_0_6026": "Beispiele: {0}", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Übermäßige Stapeltiefe beim Vergleichen der Typen \"{0}\" und \"{1}\".", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "{0}-{1} Typar<PERSON><PERSON> erwartet; geben <PERSON>e diese mit einem @extends-Tag an.", "Expected_0_arguments_but_got_1_2554": "{0} <PERSON><PERSON><PERSON><PERSON> wurden erwartet, empfangen wurden aber {1}.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "<PERSON><PERSON> wurden {0} <PERSON><PERSON><PERSON><PERSON> er<PERSON>, aber {1} erhalten. Sollte \"void\" in Ihr Typargument in \"Promise\" eingeschlossen werden?", "Expected_0_type_arguments_but_got_1_2558": "{0} Typenar<PERSON><PERSON> wurden erwartet, empfangen wurden aber {1}.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "{0} Typar<PERSON><PERSON> erwartet; geben <PERSON>e diese mit einem @extends-Tag an.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "1 Argument erwartet, aber 0 erhalten. „new Promise()“ benötigt einen JSDoc-Hinweis, um einen „resolve“ zu erzeugen, der ohne Argumente aufgerufen werden kann.", "Expected_at_least_0_arguments_but_got_1_2555": "Mindestens {0} <PERSON><PERSON><PERSON><PERSON> wurden erwartet, empfangen wurden aber {1}.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Das entsprechende schließende JSX-Tag wurde für \"{0}\" erwartet.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "<PERSON><PERSON><PERSON> das JSX-Fragment wurde das entsprechende schließende Tag erwartet.", "Expected_for_property_initializer_1442": "<PERSON><PERSON>r den Eigenschafteninitialisierer wurde \"=\" erwartet.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Der erwartete Typ des Felds \"{0}\" in der Datei \"package.json\" lautet \"{1}\", empfangen wurde \"{2}\".", "Experimental_support_for_decorators_is_a_feature_that_is_subject_to_change_in_a_future_release_Set_t_1219": "Die experimentelle Unterstützung für decorator-Elemente ist ein Feature, das in zukünftigen Versionen Änderungen unterliegt. Legen Sie die Option \"-experimentalDecorators\" in Ihrer \"tsconfig\" oder \"jsconfig\" fest, um diese Warnung zu entfernen.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Explizit angegebene Art der Modulauflösung: \"{0}\".", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "Die Potenzierung kann für bigint-Werte nur durchgeführt werden, wenn die Option \"target\" auf \"es2016\" oder höher festgelegt ist.", "Export_0_from_module_1_90059": "Exportier<PERSON> von \"{0}\" aus Modul \"{1}\"", "Export_all_referenced_locals_90060": "Alle referenzierten lokalen Elemente exportieren", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Die Exportzuweisung darf nicht verwendet werden, wenn das Ziel ECMAScript-Module sind. Verwenden Sie stattdessen ggf. \"export default\" oder ein anderes Modulformat.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "Die Exportzuweisung wird nicht unterstützt, wenn das Flag \"-module\" den Wert \"system\" aufweist.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Die Exportdeklaration verursacht einen Konflikt mit der exportierten Deklaration von \"{0}\".", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Exportdeklarationen sind in einem Namespace unzulässig.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "Der Exportspezifizierer \"{0}\" ist im package.json-Bereich beim Pfad \"{1}\" nicht vorhanden.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "Der exportierte Typalias \"{0}\" besitzt oder verwendet den privaten Namen \"{1}\".", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "Der exportierte Typalias \"{0}\" besitzt oder verwendet den privaten Namen \"{1}\" aus dem Modul \"{2}\".", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "Die exportierte Variable \"{0}\" besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "Die exportierte Variable \"{0}\" besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Exported_variable_0_has_or_is_using_private_name_1_4025": "Die exportierte Variable \"{0}\" besitzt oder verwendet den privaten Namen \"{1}\".", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Exporte und Exportzuweisungen sind in Modulerweiterungen unzulässig.", "Expression_expected_1109": "<PERSON>s wurde ein Ausdruck erwartet.", "Expression_or_comma_expected_1137": "<PERSON>s wurde ein Ausdruck oder Komma erwartet.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "Der Ausdruck erzeugt einen Tupeltyp, der für die Darstellung zu groß ist.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "Der Ausdruck erzeugt einen union-Typ, der für die Darstellung zu komplex ist.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "Der Ausdruck wird in \"_super\" aufgelöst. Damit erfasst der Compiler den Basisklassenverweis.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "Der Ausdruck wird in die Variablendeklaration \"_newTarget\" auf<PERSON><PERSON><PERSON>, die der Compiler zum Erfassen der Metaeigenschaftenreferenz \"new.target\" verwendet.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "Der Ausdruck wird in die Variablendeklaration \"_this\" auf<PERSON><PERSON><PERSON>, die der Compiler verwendet, um den this-Verweis zu erfassen.", "Extract_constant_95006": "Konstante extrahieren", "Extract_function_95005": "Funktion extrahieren", "Extract_to_0_in_1_95004": "Als {0} nach {1} extrahieren", "Extract_to_0_in_1_scope_95008": "Als {0} in {1}-Be<PERSON>ich extrahieren", "Extract_to_0_in_enclosing_scope_95007": "Als {0} in einschließenden Bereich extrahieren", "Extract_to_interface_95090": "In Schnittstelle extrahieren", "Extract_to_type_alias_95078": "In Typalias extrahieren", "Extract_to_typedef_95079": "In TypeDef extrahieren", "Extract_type_95077": "<PERSON><PERSON>", "FILE_6035": "DATEI", "FILE_OR_DIRECTORY_6040": "DATEI ODER VERZEICHNIS", "Failed_to_parse_file_0_Colon_1_5014": "<PERSON><PERSON> beim Analy<PERSON> \"{0}\": {1}.", "Fallthrough_case_in_switch_7029": "FallThrough-Fall in switch-Anweisung.", "File_0_does_not_exist_6096": "<PERSON> Datei \"{0}\" ist nicht vorhanden.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "Die Datei \"{0}\" ist gemäß früheren zwischengespeicherten Lookups nicht vorhanden.", "File_0_exist_use_it_as_a_name_resolution_result_6097": "Die Datei \"{0}\" ist vorhanden – sie wird als Ergebnis der Namensauflösung verwendet.", "File_0_exists_according_to_earlier_cached_lookups_6239": "Die Datei \"{0}\" ist gemäß früheren zwischengespeicherten Lookups vorhanden.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "Die Datei \"{0}\" weist eine nicht unterstützte Erweiterung auf. Es werden nur die folgenden Erweiterungen unterstützt: {1}.", "File_0_has_an_unsupported_extension_so_skipping_it_6081": "Die Datei \"{0}\" hat eine nicht unterstützte Erweiterung und wird übersprungen.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "Die Datei \"{0}\" ist eine JavaScript-Datei. Wollten Sie die Option \"allowJs\" aktivieren?", "File_0_is_not_a_module_2306": "<PERSON> Datei \"{0}\" ist kein Modul.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "<PERSON> Datei \"{0}\" befindet sich nicht in der Dateiliste von Projekt \"{1}\". Projekte müssen alle Dateien auflisten oder ein include-<PERSON><PERSON> verwenden.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "Datei \"{0}\" befindet sich nicht unter \"rootDir\" \"{1}\". \"rootDir\" muss alle Quelldateien enthalten.", "File_0_not_found_6053": "<PERSON> Datei \"{0}\" wurde nicht gefunden.", "File_Management_6245": "Dateiverwaltung", "File_change_detected_Starting_incremental_compilation_6032": "Es wurde eine Dateiänderung erkannt. Die inkrementelle Kompilierung wird gestartet...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "Die Datei ist ein CommonJS-<PERSON><PERSON><PERSON>, da '{0}' nicht das Feld „Typ“ aufweist", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "Die Datei ist ein CommonJS-<PERSON><PERSON><PERSON>, da '{0}' das Feld „Typ“ aufweist, dessen Wert nicht „Modul“ ist", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "Die Datei ist ein CommonJS-Modul, da „package.json“ nicht gefunden wurde", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "Die Datei ist ein ECMAScript-<PERSON><PERSON><PERSON>, da '{0}' das Feld „Typ“ mit dem Wert „Modul“ aufweist.", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Die Datei ist ein CommonJS-Modul und kann möglicherweise in ein ES-Modul konvertiert werden.", "File_is_default_library_for_target_specified_here_1426": "Die Datei ist die Standardbibliothek für das hier angegebene Ziel.", "File_is_entry_point_of_type_library_specified_here_1419": "Die Datei ist ein Einstiegspunkt der hier angegebenen Typbibliothek.", "File_is_included_via_import_here_1399": "Die Datei wird hier per Import eingeschlossen.", "File_is_included_via_library_reference_here_1406": "Die Datei wird hier per Bibliotheksverweis eingeschlossen.", "File_is_included_via_reference_here_1401": "Die Datei wird hier per Verweis eingeschlossen.", "File_is_included_via_type_library_reference_here_1404": "Die Datei wird hier per Typbibliotheksverweis eingeschlossen.", "File_is_library_specified_here_1423": "Die Datei ist die hier angegebene Bibliothek.", "File_is_matched_by_files_list_specified_here_1410": "Die Datei wird mit der hier angegebenen Liste \"files\" abgeglichen.", "File_is_matched_by_include_pattern_specified_here_1408": "Die Datei wird mit dem hier angegebenen include-Muster abgeglichen.", "File_is_output_from_referenced_project_specified_here_1413": "Die Datei ist die Ausgabe des hier angegebenen referenzierten Projekts.", "File_is_output_of_project_reference_source_0_1428": "Die Datei ist die Ausgabe der Projektverweisquelle \"{0}\".", "File_is_source_from_referenced_project_specified_here_1416": "Die Datei ist die Quelle des hier angegebenen referenzierten Projekts.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "Der Dateiname \"{0}\" unterscheidet sich vom bereits enthaltenen Dateinamen \"{1}\" nur hinsichtlich der Groß-/Kleinschreibung.", "File_name_0_has_a_1_extension_stripping_it_6132": "Der Dateiname \"{0}\" weist eine Erweiterung \"{1}\" auf. <PERSON><PERSON> wird entfernt.", "File_redirects_to_file_0_1429": "Die Datei leitet an die Datei \"{0}\" um.", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "Die Dateispezifikation darf kein übergeordnetes Verzeichnis (\"..\") enthalten, das nach einem rekursiven Verzeichnisplatzhalter (\"**\") angegeben wird: \"{0}\".", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "Die Dateispezifikation darf nicht mit einem rekursiven Verzeichnisplatzhalter (\"**\") enden: \"{0}\".", "Filters_results_from_the_include_option_6627": "Filtert Ergebnisse aus der Option \"include\".", "Fix_all_detected_spelling_errors_95026": "Alle erkannten Rechtschreibfehler korrigieren", "Fix_all_expressions_possibly_missing_await_95085": "Korrigieren Sie alle Ausdrücke, in denen \"await\" möglicherweise fehlt.", "Fix_all_implicit_this_errors_95107": "Alle impliziten this-<PERSON><PERSON> beheben", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Alle falschen Rückgabetypen einer asynchronen Funktionen korrigieren", "For_await_loops_cannot_be_used_inside_a_class_static_block_18038": "\"For await\"-Schleifen können nicht innerhalb eines statischen Klassenblocks verwendet werden.", "Found_0_errors_6217": "{0} <PERSON>hler gefunden.", "Found_0_errors_Watching_for_file_changes_6194": "{0} Fehler gefunden. Es wird auf Dateiänderungen überwacht.", "Found_0_errors_in_1_files_6261": "In {1} <PERSON><PERSON> wurden {0} Fehler gefunden.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "<PERSON><PERSON> wurden {0} <PERSON><PERSON> in derselben Datei gefunden, beginnend bei: {1}", "Found_1_error_6216": "1 Fehler gefunden.", "Found_1_error_Watching_for_file_changes_6193": "1 Fehler gefunden. Es wird auf Dateiänderungen überwacht.", "Found_1_error_in_1_6259": "1 Fehler in {1} gefunden", "Found_package_json_at_0_6099": "\"package.json\" wurde unter \"{0}\" gefunden.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_1250": "Funktionsdeklarationen sind in Blöcken im Strict-Modus unzulässig, wenn das Ziel \"ES3\" oder \"ES5\" ist.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Class_d_1251": "Funktionsdeklarationen sind in Blöcken im Strict-Modus unzulässig, wenn das Ziel \"ES3\" oder \"ES5\" ist. Klassendefinitionen befinden sich automatisch im Strict-Modus.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Modules_1252": "Funktionsdeklarationen sind in Blöcken im Strict-Modus unzulässig, wenn das Ziel \"ES3\" oder \"ES5\" ist. Module befinden sich automatisch im Strict-Modus.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "Ein Funktionsausdruck ohne Rückgabetypanmerkung weist implizit einen {0}-Rückgabetyp auf.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "Die Funktionsimplementierung fehlt oder folgt nicht unmittelbar auf die Deklaration.", "Function_implementation_name_must_be_0_2389": "Der Name der Funktionsimplementierung muss \"{0}\" lauten.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "Die Funktion weist implizit den Typ \"any\" auf, weil keine Rückgabetypanmerkung vorhanden ist und darauf direkt oder indirekt in einem ihrer Rückgabeausdrücke verwiesen wird.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "Der Funktion fehlt die abschließende return-Anweisung, und der Rückgabetyp enthält nicht \"undefined\".", "Function_not_implemented_95159": "Die Funktion ist nicht implementiert.", "Function_overload_must_be_static_2387": "Die Funktionsüberladung muss statisch sein.", "Function_overload_must_not_be_static_2388": "Die Funktionsüberladung darf nicht statisch sein.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "Die Notation des Funktionstyps muss in Klammern gesetzt werden, wenn sie in einem Union-Typ verwendet wird.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "Die Notation des Funktionstyps muss in Klammern gesetzt werden, wenn sie in einem Intersection-Typ verwendet wird.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Ein Funktionstyp ohne Rückgabetypanmerkung weist implizit einen Rückgabetyp \"{0}\" auf.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "Eine Funktion mit Textkörpern kann nur mit Umgebungsklassen zusammengeführt werden.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Generieren Sie .d.ts-Dateien aus TypeScript- und JavaScript-Dateien in Ihrem Projekt.", "Generate_get_and_set_accessors_95046": "GET- und SET-Accessoren generieren", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "get- und set-Zugriffsmethoden für alle überschreibenden Eigenschaften generieren", "Generates_a_CPU_profile_6223": "Generiert ein CPU-Profil.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Generiert eine sourcemap für jede entsprechende .d.ts-Datei.", "Generates_an_event_trace_and_a_list_of_types_6237": "Generiert eine Ereignisablaufverfolgung und eine Liste von Typen.", "Generates_corresponding_d_ts_file_6002": "Generiert die entsprechende .d.ts-Datei.", "Generates_corresponding_map_file_6043": "Generiert die entsprechende MAP-Datei.", "Generator_implicitly_has_yield_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_retu_7025": "Der Generator weist implizit den yield-Typ \"{0}\" auf, weil er keine Werte ausgibt. Erwägen Sie die Angabe einer Rückgabetypanmerkung.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Generatoren sind in einem Umgebungskontext unzulässig.", "Generic_type_0_requires_1_type_argument_s_2314": "Der generische Typ \"{0}\" erfordert {1} Typargument(e).", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "Der generische Typ \"{0}\" ben<PERSON><PERSON><PERSON> z<PERSON> {1} und {2} Typargumente.", "Global_module_exports_may_only_appear_at_top_level_1316": "Globale Modulexporte dürfen nur auf der obersten Ebene auftreten.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Globale Modulexporte dürfen nur in Deklarationsdateien auftreten.", "Global_module_exports_may_only_appear_in_module_files_1314": "Globale Modulexporte dürfen nur in Moduldateien auftreten.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Der globale Typ \"{0}\" muss eine Klassen- oder Schnittstellentyp sein.", "Global_type_0_must_have_1_type_parameter_s_2317": "Der globale Typ \"{0}\" muss {1} Typparameter aufweisen.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Legen Sie für Neukompilierungen in \"--incremental\" und \"--watch\" fest, dass sich Änderungen innerhalb einer Datei nur auf die direkt davon abhängigen Dateien auswirken.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Bei Neukompilierungen in Projekten, die die Modi „incremental“ und „watch“ verwenden, wird davon ausgegangen, dass Änderungen innerhalb einer Datei sich nur direkt auf Dateien auswirken.", "Hexadecimal_digit_expected_1125": "<PERSON>s wurde eine hexadezimale Zahl erwartet.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Bezeichner erwartet. \"{0}\" ist ein reserviertes Wort auf der obersten Ebene eines Moduls.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Ein Bezeichner wird erwartet. \"{0}\" ist ein reserviertes Wort im Strict-Modus.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Es wurde ein Bezeichner erwartet. \"{0}\" ist ein reserviertes Wort im Strict-Modus. Klassendefinitionen befinden sich automatisch im Strict-Modus.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Es wurde ein Bezeichner erwartet. \"{0}\" ist ein reserviertes Wort im Strict-Modus. Module befinden sich automatisch im Strict-Modus.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Bezeichner erwartet. \"{0}\" ist ein reserviertes Wort, das hier nicht verwendet werden kann.", "Identifier_expected_1003": "<PERSON>s wurde ein Bezeichner erwartet.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Bezeichner erwartet. \"__esModule\" ist als exportierter Marker für die Umwandlung von ECMAScript-Modulen reserviert.", "Identifier_or_string_literal_expected_1478": "Bezeichner oder Zeichenfolgenliteral erwartet.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "<PERSON><PERSON> das Paket \"{0}\" dieses <PERSON><PERSON><PERSON> tats<PERSON>chlich verfü<PERSON><PERSON> macht, erw<PERSON><PERSON>, einen Pull Request zum Ändern von https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1} zu senden.", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "<PERSON><PERSON> Paket \"{0}\" dieses <PERSON><PERSON><PERSON> tatsächlich verfüg<PERSON> macht, ve<PERSON><PERSON><PERSON>, eine neue Deklarationsdatei (.d.ts) hi<PERSON><PERSON>ufügen, die Declare-Modul \"{1}\" enthält.", "Ignore_this_error_message_90019": "<PERSON>se <PERSON>dung ignorieren", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Ignoriert tsconfig.json und kompiliert die angegebenen Dateien mit den Standardkompilieroptionen.", "Implement_all_inherited_abstract_classes_95040": "Alle geerbten abstrakten Klassen implementieren", "Implement_all_unimplemented_interfaces_95032": "Alle nicht implementierten Schnittstellen implementieren", "Implement_inherited_abstract_class_90007": "Geerbte abstrakte Klasse implementieren", "Implement_interface_0_90006": "Schnittstelle \"{0}\" implementieren", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "Die implements-Klausel der exportierten Klasse \"{0}\" besitzt oder verwendet den privaten Namen \"{1}\".", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "Die implizite Konvertierung von \"symbol\" in \"string\" führt zur Laufzeit zu einem Fehler. Erwägen Sie, diesen Ausdruck in \"String(...)\" einzuschließen.", "Import_0_from_1_90013": "\"{0}\" aus \"{1}\" importieren", "Import_assertion_values_must_be_string_literal_expressions_2837": "Importassertionswerte müssen Zeichenfolgenliteralausdrücke sein.", "Import_assertions_are_not_allowed_on_statements_that_transpile_to_commonjs_require_calls_2836": "Importassertionen sind für Anweisungen, die in „require“-<PERSON><PERSON><PERSON><PERSON> von CommonJs transpilieren, nicht zulässig.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_or_nodenext_2821": "Importassertionen werden nur unterstützt, wenn die Option „--module“ auf „esnext“ oder „nodenext“ festgelegt ist.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Importassertionen können nicht mit rein typbasierten Importen oder Exporten verwendet werden.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Die Importzuweisung kann nicht verwendet werden, wenn das Ziel ECMAScript-Module sind. Verwenden Sie stattdessen ggf. \"import * as ns from 'mod'\", \"import {a} from 'mod'\", \"import d from 'mod'\" oder ein anderes Modulformat.", "Import_declaration_0_is_using_private_name_1_4000": "Die Importdeklaration \"{0}\" verwendet den privaten Namen \"{1}\".", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "Die Importdeklaration verursacht einen Konflikt mit der lokalen Deklaration von \"{0}\".", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Importdeklarationen in einem Namespace dürfen nicht auf ein Modul verweisen.", "Import_emit_helpers_from_tslib_6139": "Ausgabehilfsprogramme aus \"tslib\" importieren.", "Import_may_be_converted_to_a_default_import_80003": "Der Import kann in einen Standardimport konvertiert werden.", "Import_name_cannot_be_0_2438": "Der Importname darf nicht \"{0}\" sein.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Import- oder Exportdeklaration in einer Umgebungsmoduldeklaration dürfen nicht über den relativen Modulnamen auf ein Modul verweisen.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "Der Importspezifizierer \"{0}\" ist im package.json-Bereich beim Pfad \"{1}\" nicht vorhanden.", "Imported_via_0_from_file_1_1393": "Importier<PERSON> über \"{0}\" aus der Datei \"{1}\"", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Importiert über \"{0}\" aus der Datei \"{1}\" zum Importieren von \"importHelpers\", wie in \"compilerOptions\" angegeben", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Importiert über \"{0}\" aus der Datei \"{1}\" zum Importieren der Factoryfunktionen \"jsx\" und \"jsxs\"", "Imported_via_0_from_file_1_with_packageId_2_1394": "Importiert über \"{0}\" aus der Datei \"{1}\" mit packageId \"{2}\"", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Importiert über \"{0}\" aus der Datei \"{1}\" mit packageId \"{2}\" zum Importieren von \"importHelpers\", wie in \"compilerOptions\" angegeben", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Importiert über \"{0}\" aus der Datei \"{1}\" mit packageId \"{2}\" zum Importieren der Factoryfunktionen \"jsx\" und \"jsxs\"", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Importe sind in Modulerweiterungen unzulässig. Verschieben Sie diese ggf. in das einschließende externe Modul.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "In Umgebungsenumerationsdeklarationen muss der Memberinitialisierer ein konstanter Ausdruck sein.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "In einer Enumeration mit mehreren Deklarationen kann nur eine Deklaration einen Initialisierer für das erste Enumerationselement ausgeben.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Schl<PERSON>ßen Sie eine Liste von Dateien ein. Dies unterstützt keine Globmuster im Gegensatz zu \"include\".", "Include_modules_imported_with_json_extension_6197": "Importierte Module mit der Erweiterung \"JSON\" einschließen", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Fügen Sie den Quellcode in die Quellzuordnungen innerhalb des ausgesendeten JavaScript-Codes ein.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Schließen Sie Quellzuordnungsdateien in das ausgegebene JavaScript ein.", "Includes_imports_of_types_referenced_by_0_90054": "<PERSON><PERSON><PERSON><PERSON>t Importe von <PERSON> e<PERSON>, auf die von „{0}“ verwiesen wird", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "Bei Einschließung von --watch beginnt -w, das aktuelle Projekt auf Dateiänderungen zu überwachen. <PERSON><PERSON> eingestellt, können Sie den Überwachungsmodus konfigurieren, und zwar mit:", "Index_signature_for_type_0_is_missing_in_type_1_2329": "Die Indexsignatur für den Typ \"{0}\" fehlt im Typ \"{1}\".", "Index_signature_in_type_0_only_permits_reading_2542": "Die Indexsignatur in Typ \"{0}\" lässt nur Lesevorgänge zu.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Einzelne Deklarationen in der gemergten Deklaration \"{0}\" müssen alle exportiert oder alle lokal sein.", "Infer_all_types_from_usage_95023": "Alle Typen aus der Syntax ableiten", "Infer_function_return_type_95148": "Funktionsrückgabetyp ableiten", "Infer_parameter_types_from_usage_95012": "Parametertypen aus der Nutzung ableiten", "Infer_this_type_of_0_from_usage_95080": "Typ \"this\" von \"{0}\" aus Syntax ableiten", "Infer_type_of_0_from_usage_95011": "<PERSON><PERSON> <PERSON> \"{0}\" aus der Nutzung ableiten", "Initialize_property_0_in_the_constructor_90020": "Eigenschaft \"{0}\" im Konstruktor initialisieren", "Initialize_static_property_0_90021": "Statische Eigenschaft \"{0}\" initialisieren", "Initializer_for_property_0_2811": "Initialisierer für Eigenschaft \"{0}\"", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "Der Initialisierer der Instanzmembervariablen \"{0}\" darf nicht auf den im Konstruktor deklarierten Bezeichner \"{1}\" verweisen.", "Initializer_provides_no_value_for_this_binding_element_and_the_binding_element_has_no_default_value_2525": "Der Initialisierer stellt keinen Wert für dieses Bindungselement bereit, und das Bindungselement besitzt keinen Standardwert.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Initialisierer sind in Umgebungskontexten unzulässig.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Initialisiert ein TypeScript-Projekt und erstellt eine Datei \"tsconfig.json\".", "Insert_command_line_options_and_files_from_a_file_6030": "<PERSON><PERSON><PERSON>szeilenoptionen und Dateien aus einer Datei ein.", "Install_0_95014": "\"{0}\" installieren", "Install_all_missing_types_packages_95033": "Alle fehlenden Typenpakete installieren", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "Die Schnittstelle \"{0}\" kann die Typen \"{1}\" und \"{2}\" nicht gleichzeitig erweitern.", "Interface_0_incorrectly_extends_interface_1_2430": "Die Schnittstelle \"{0}\" erweitert fälschlicherweise die Schnittstelle \"{1}\".", "Interface_declaration_cannot_have_implements_clause_1176": "Die Schnittstellendeklarationen darf keine implements-<PERSON><PERSON>.", "Interface_must_be_given_a_name_1438": "Schnittstelle muss einen Namen erhalten.", "Interface_name_cannot_be_0_2427": "Der Schnittstellenname darf nicht \"{0}\" sein.", "Interop_Constraints_6252": "Interop-Einschränkungen", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interpretieren Sie optionale Eigenschaftstypen als geschrieben, statt 'nicht definiert' hinzuzufügen.", "Invalid_character_1127": "Ungültiges Zeichen.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "Der ungültige Importbezeichner \"{0}\" weist keine möglichen Auflösungen auf.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Ungültiger Modulname in Augmentation. Das Modul \"{0}\" wird in ein nicht typisiertes Modul in \"{1}\" auf<PERSON><PERSON><PERSON>, das nicht augmentiert werden kann.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Ungültiger Modulname in der Erweiterung. Das Modul \"{0}\" wurde nicht gefunden.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Ungültige optionale Kette aus neuem Ausdruck. Wollten Sie '{0}()' anrufen?", "Invalid_reference_directive_syntax_1084": "Ungültige Syntax der reference-Direktive.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Ungültige Verwendung von \"{0}\". Es kann nicht innerhalb eines statischen Klassenblocks verwendet werden.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Ungültige Verwendung von \"{0}\". Module befinden sich automatisch im Strict-Modus.", "Invalid_use_of_0_in_strict_mode_1100": "Ungültige Verwendung von \"{0}\" im Strict-Modus.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Ungültiger Wert für \"jsxFactory\". \"{0}\" ist kein gültiger Bezeichner oder qualifizierter Name.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Ungültiger Wert für \"jsxFragmentFactory\". \"{0}\" ist kein gültiger Bezeichner oder qualifizierter Name.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Ungültiger Wert für \"-reactNamespace\". \"{0}\" ist kein gültiger Bezeichner.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Möglicherweise fehlt ein Komma, um diese beiden Vorlagenausdrücke zu trennen. Sie bilden einen Vorlagenausdruck mit Tags, der nicht aufgerufen werden kann.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "Der zugehörige Elementtyp \"{0}\" ist kein gültiges JSX-Element.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "Der zugehörige Instanztyp \"{0}\" ist kein gültiges JSX-Element.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "Der Rückgabetyp \"{0}\" ist kein gültiges JSX-Element.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "JSD<PERSON> \"@{0} {1}\" entspricht nicht der Klausel \"extends {2}\".", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc \"@{0}\" ist keiner Klassendeklaration zugeordnet.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "Das JSDoc-Tag \"...\" wird möglicherweise nur im letzten Parameter einer Signatur angezeigt.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "Das JSDoc-Tag \"@param\" weist den Namen \"{0}\" auf, es gibt jedoch keinen Parameter dieses Namens.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "Das JSDoc-Tag \"@param\" weist den Namen \"{0}\" auf, es ist jedoch kein Parameter dieses Namens vorhanden. Es läge eine Übereinstimmung mit \"arguments\" vor, wenn ein Arraytyp vorläge.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "Das JSDoc-Tag \"@typedef\" muss entweder eine Typanmerkung aufweisen, oder die Tags \"@property\" oder \"@member\" müssen darauf folgen.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc-Typen können nur innerhalb von Dokumentationskommentaren verwendet werden.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc-Typen können in TypeScript-Typen verschoben werden.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "JSX-Attributen darf nur ein nicht leeres expression-Objekt zugewiesen werden.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "Das JSX-Element \"{0}\" weist kein entsprechendes schließendes Tag auf.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "Die JSX-Elementklasse unterstützt keine Attribute, weil sie keine Eigenschaft \"{0}\" aufweist.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "Das JSX-Element enthält implizit den Typ \"any\", weil keine Schnittstelle \"JSX.{0}\" vorhanden ist.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "Das JSX-Element enthält implizit den Typ \"any\", weil der globale Typ \"JSX.Element\" nicht vorhanden ist.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "Der JSX-Elementtyp \"{0}\"weist keine Konstrukt- oder Aufrufsignaturen auf.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX-Elemente dürfen nicht mehrere Attribute mit dem gleichen Namen aufweisen.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "JSX-Ausdrücke dürfen keinen Komma-Operator verwenden. Wollten Si<PERSON> ein A<PERSON> schreiben?", "JSX_expressions_must_have_one_parent_element_2657": "JSX-Ausdrücke müssen ein übergeordnetes Element aufweisen.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "Das JSX-Fragment weist kein entsprechendes schließendes Tag auf.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Ausdrücke für den Zugriff auf JSX-Eigenschaften dürfen keine JSX-Namespacenamen enthalten.", "JSX_spread_child_must_be_an_array_type_2609": "Die untergeordnete JSX-Verteilung muss ein Arraytyp sein.", "JavaScript_Support_6247": "JavaScript-Unterstützung", "Jump_target_cannot_cross_function_boundary_1107": "Das Sprungziel darf die Funktionsgrenze nicht überschreiten.", "KIND_6034": "ART", "Keywords_cannot_contain_escape_characters_1260": "Schlüsselwörter können keine Escapezeichen enthalten.", "LOCATION_6037": "SPEICHERORT", "Language_and_Environment_6254": "Sprache und Umgebung", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Die linke Seite des Kommaoperators wird nicht verwendet besitzt keine Nebenwirkungen.", "Library_0_specified_in_compilerOptions_1422": "In \"compilerOptions\" angegebene Bibliothek \"{0}\"", "Library_referenced_via_0_from_file_1_1405": "Bibliothek, die über \"{0}\" aus der Datei \"{1}\" referenziert wird", "Line_break_not_permitted_here_1142": "Ein Zeilenumbruch ist hier unzulässig.", "Line_terminator_not_permitted_before_arrow_1200": "Das Zeilenabschlusszeichen ist vor dem Pfeil unzulässig.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Liste der Dateinamensuffixe, die beim Auflösen eines Moduls gesucht werden sollen.", "List_of_folders_to_include_type_definitions_from_6161": "Liste der Ordner, aus denen Typendefinitionen einbezogen werden sollen.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Liste der Stammordner, deren kombinierter Inhalt die Struktur des Projekts zur Laufzeit darstellt.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "\"{0}\" wird aus dem Stammverzeichnis \"{1}\" geladen. Speicherort des Kandidaten \"{2}\".", "Loading_module_0_from_node_modules_folder_target_file_type_1_6098": "<PERSON><PERSON><PERSON> \"{0}\" wird aus dem Ordner \"node_modules\" geladen, die Zieldatei ist vom Typ \"{1}\".", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_type_1_6095": "<PERSON><PERSON><PERSON> wird als Datei/Ordner geladen, der Speicherort des Kandidatenmoduls ist \"{0}\", die Zieldatei ist vom Typ \"{1}\".", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "<PERSON>ür das Gebietsschema ist das Format <Sprache> oder <Sprache>-<Gebiet> <PERSON><PERSON><PERSON><PERSON>, z. B. \"{0}\" oder \"{1}\".", "Log_paths_used_during_the_moduleResolution_process_6706": "Protokollpfade, die während des „moduleResolution“-Prozesses verwendet werden.", "Longest_matching_prefix_for_0_is_1_6108": "Das längste übereinstimmende Präfix für \"{0}\" ist \"{1}\".", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Die Suche erfolgt im Ordner \"node_modules\". Anfangsspeicherort \"{0}\".", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Alle \"super()\"-Aufrufe als erste Anweisung im entsprechenden Konstruktor festlegen", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "<PERSON><PERSON><PERSON>, dass keyof nur <PERSON>n, <PERSON><PERSON><PERSON> <PERSON>, <PERSON>ahlen oder Symbolen zurückgibt. Legacy-Option.", "Make_super_call_the_first_statement_in_the_constructor_90002": "super()-<PERSON><PERSON><PERSON><PERSON> als erste Anweisung im Konstruktor festlegen", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Der zugeordnete Objekttyp weist implizit einen any-Vorlagentyp auf.", "Matched_0_condition_1_6403": "Übereinstimmung mit \"{0}\" Bedingung \"{1}\".", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Standardmäßig zugeordnetes Includemuster „**/*“", "Matched_by_include_pattern_0_in_1_1407": "Abge<PERSON>chen mit dem include-Muster \"{0}\" in \"{1}\"", "Member_0_implicitly_has_an_1_type_7008": "<PERSON> Member \"{0}\" weist implizit den Typ \"{1}\" auf.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "Member \"{0}\" weist implizit einen <PERSON>p \"{1}\" auf, möglicherweise kann jedoch ein besserer Typ aus der Syntax abgeleitet werden.", "Merge_conflict_marker_encountered_1185": "Mergekonfliktmarkierung", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "Die gemergte Deklaration \"{0}\" darf keine Exportstandarddeklaration enthalten. Fügen Sie ggf. eine separate Deklaration \"export default {0}\" hinzu.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "Die Metaeigenschaft \"{0}\" ist nur im Text einer Funktionsdeklaration, eines Funktionsausdrucks oder eines Konstruktors zulässig.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "Die Methode \"{0}\" darf keine Implementierung besitzen, weil sie als abstrakt markiert ist.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Die Methode \"{0}\" der exportierten Schnittstelle besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Die Methode \"{0}\" der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Method_not_implemented_95158": "Die Methode ist nicht implementiert.", "Modifiers_cannot_appear_here_1184": "Modifizierer dürfen hier nicht enthalten sein.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "<PERSON> Modul \"{0}\" kann nur mit dem Flag \"{1}\" als Standard importiert werden.", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "Das Modul \"{0}\" kann nicht mit diesem Konstrukt importiert werden. Der Spezifizierer wird nur in ein ES-Modul aufgelöst, das nicht mit \"require\" importiert werden kann. Verwenden Sie stattdessen einen ECMAScript-Import.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "<PERSON> Modul \"{0}\" deklariert \"{1}\" lokal, der Export erfolgt jedoch als \"{2}\".", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "<PERSON> Modul \"{0}\" deklariert \"{1}\" lokal, es erfolgt jedoch kein Export.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "<PERSON> Modul \"{0}\" verweist nicht auf einen <PERSON>, wird hier aber als Typ verwendet. Meinten Sie \"typeof import('{0}')\"?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "<PERSON> Modul \"{0}\" verweist nicht auf einen Wert, wird hier aber als Wert verwendet.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Das Modul \"{0}\" hat bereits einen Member mit dem Namen \"{1}\" exportiert. Erwägen Sie, ihn explizit erneut zu exportieren, um die Mehrdeutigkeit zu vermeiden.", "Module_0_has_no_default_export_1192": "<PERSON> Modul \"{0}\" weist keinen Standardexport auf.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "<PERSON> Modul \"{0}\" weist keinen Standardexport auf. Wollten Sie stattdessen \"import { {1} } from {0}\" verwenden?", "Module_0_has_no_exported_member_1_2305": "<PERSON> Modul \"{0}\" weist keinen exportierten Member \"{1}\" auf.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "Das Modul \"{0}\" umfasst keinen exportierten Member \"{1}\". Wollten Sie stattdessen \"import {1} from {0}\" verwenden?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "<PERSON> Modul \"{0}\" wird durch eine lokale Deklaration mit dem gleichen Namen ausgeblendet.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "Das Modul \"{0}\" verwendet \"export =\" und darf nicht mit \"export *\" verwendet werden.", "Module_0_was_resolved_as_ambient_module_declared_in_1_since_this_file_was_not_modified_6145": "Das Modul \"{0}\" wurde als in \"{1}\" deklariertes Umgebungsmodul aufgelöst, weil diese Datei nicht geändert wurde.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "Das Modul \"{0}\" wurde als lokal deklariertes Umgebungsmodul in der Datei \"{1}\" aufgelöst.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "<PERSON> Modul \"{0}\" wurde zu \"{1}\" aufgelöst, aber \"--jsx\" wurde nicht festgelegt.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "<PERSON> Modul \"{0}\" wurde in \"{1}\" aufgelöst, aber \"--resolveJsonModule\" wird nicht verwendet.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Namen der Moduldeklaration dürfen nur Zeichenfolgen enthalten, die von ' oder \" eingeschlossen werden.", "Module_name_0_matched_pattern_1_6092": "<PERSON><PERSON><PERSON><PERSON> \"{0}\", übereinstimmendes Muster \"{1}\".", "Module_name_0_was_not_resolved_6090": "======== Der Modulname \"{0}\" wurde nicht aufgelöst. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Der Modulname \"{0}\" wurde erfolgreich in \"{1}\" aufgelöst. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== Der Modulname \"{0}\" wurde erfolgreich in \"{1}\" mit Paket-ID \"{2}\" aufgelöst. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "Die Art der Modulauflösung wird nicht angegeben. \"{0}\" wird verwendet.", "Module_resolution_using_rootDirs_has_failed_6111": "Fehler bei der Modulauflösung mithilfe von \"rootDirs\".", "Modules_6244": "<PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Modifizierer für bezeichnete Tupelelemente in Bezeichnungen verschieben", "Move_to_a_new_file_95049": "In neue Datei verschieben", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Me<PERSON>ere aufeinander folgende numerische Trennzeichen sind nicht zulässig.", "Multiple_constructor_implementations_are_not_allowed_2392": "Mehrere Konstruktorimplementierungen sind unzulässig.", "NEWLINE_6061": "NEUE ZEILE", "Name_is_not_valid_95136": "Der Name ist ungültig.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "Die benannte Eigenschaft \"{0}\" der Typen \"{1}\" und \"{2}\" ist nicht identisch.", "Namespace_0_has_no_exported_member_1_2694": "Der Namespace \"{0}\" besitzt keinen exportierten Member \"{1}\".", "Namespace_must_be_given_a_name_1437": "Namespace muss einen Namen erhalten.", "Namespace_name_cannot_be_0_2819": "Namespacename darf nicht \"{0}\" sein.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "<PERSON><PERSON> Basiskonstruktor weist die angegebene Anzahl von Typargumenten auf.", "No_constituent_of_type_0_is_callable_2755": "Es ist kein Bestandteil vom Typ \"{0}\" aufrufbar.", "No_constituent_of_type_0_is_constructable_2759": "<PERSON>s kann kein Bestandteil vom Typ \"{0}\" erstellt werden.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "<PERSON><PERSON><PERSON> \"{1}\" wurde keine Indexsignatur mit einem Parameter vom Typ \"{0}\" gefunden.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "In der Konfigurationsdatei \"{0}\" wurden keine Eingaben gefunden. Als include-Pfade wurden \"{1}\", als exclude-Pfade wurden \"{2}\" angegeben.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Wird nicht mehr unterstützt. Legen Sie die Textcodierung für das Lesen von Dateien in früheren Versionen manuell fest.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "<PERSON>ine Überladung erwartet {0} Arg<PERSON>nte, aber es sind Überladungen vorhanden, die entweder {1} oder {2} Argumente erwarten.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "<PERSON>ine Überladung erwartet {0} Typargumente, aber es sind Überladungen vorhanden, die entweder {1} oder {2} Typargumente erwarten.", "No_overload_matches_this_call_2769": "<PERSON>ine Überladung stimmt mit diesem Aufruf überein.", "No_type_could_be_extracted_from_this_type_node_95134": "Aus diesem Typknoten konnte kein Typ extrahiert werden.", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "Im Bereich für die Kompakteigenschaft \"{0}\" ist kein Wert vorhanden. Deklarieren Sie entweder einen Wert, oder geben Sie einen Initialisierer an.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "Die nicht abstrakte Klasse \"{0}\" implementiert nicht den geerbten abstrakten Member \"{1}\" aus der Klasse \"{2}\".", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Der nicht abstrakte Ausdruck implementiert nicht den geerbten abstrakten Member \"{0}\" aus der Klasse \"{1}\".", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Assertionen ungleich NULL können nur in TypeScript-Dateien verwendet werden.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "Nicht relative Pfade sind nur zulässig, wenn \"baseUrl\" festgelegt wurde. Fehlt am Anfang die Zeichenfolge \"./\"?", "Non_simple_parameter_declared_here_1348": "Hier wurde ein nicht einfacher Parameter deklariert.", "Not_all_code_paths_return_a_value_7030": "Nicht alle Codepfade geben einen Wert zurück.", "Not_all_constituents_of_type_0_are_callable_2756": "Nicht alle Bestandteile vom Typ \"{0}\" können aufgerufen werden.", "Not_all_constituents_of_type_0_are_constructable_2760": "Nicht alle Bestandteile vom Typ \"{0}\" können erstellt werden.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Numerische Literale mit absoluten Werten von 2^53 oder höher sind zu groß, um als ganze Zahlen genau dargestellt werden zu können.", "Numeric_separators_are_not_allowed_here_6188": "Numerische Trennzeichen sind hier nicht zulässig.", "Object_is_of_type_unknown_2571": "Das Objekt ist vom Typ \"Unbekannt\".", "Object_is_possibly_null_2531": "Das Objekt ist möglicherweise \"NULL\".", "Object_is_possibly_null_or_undefined_2533": "Das Objekt ist möglicherweise \"NULL\" oder \"nicht definiert\".", "Object_is_possibly_undefined_2532": "Das Objekt ist möglicherweise \"nicht definiert\".", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Das Objektliteral kann nur bekannte Eigenschaften angeben, und \"{0}\" ist im Typ \"{1}\" nicht vorhanden.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Das Objektliteral gibt möglicherweise nur bekannte Eigenschaften an, \"{0}\" ist jedoch im Typ \"{1}\" nicht vorhanden. Wollten Sie \"{2}\" schreiben?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "Die Eigenschaft \"{0}\" des Objektliterals weist implizit den Typ \"{1}\" auf.", "Octal_digit_expected_1178": "<PERSON>s wurde eine Oktalzahl erwartet.", "Octal_literal_types_must_use_ES2015_syntax_Use_the_syntax_0_8017": "Oktalliteraltypen müssen die Syntax \"ES2015\" verwenden. Verwenden Sie die Syntax \"{0}\".", "Octal_literals_are_not_allowed_in_enums_members_initializer_Use_the_syntax_0_8018": "Oktalliterale sind in einem Mitgliederenumerationsinitialisierer nicht zulässig. Verwenden Sie die Syntax \"{0}\".", "Octal_literals_are_not_allowed_in_strict_mode_1121": "Oktalliterale sind im Strict-Modus unzulässig.", "Octal_literals_are_not_available_when_targeting_ECMAScript_5_and_higher_Use_the_syntax_0_1085": "Oktalliterale sind bei der Zielgruppenadressierung von ECMAScript 5 und höher nicht verfügbar. Verwenden Sie die Syntax \"{0}\".", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "In einer for...in-Anweisung ist nur eine einzige Variablendeklaration zulässig.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "In einer for...of-Anweisung ist nur eine einzige Variablendeklaration zulässig.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Nur eine void-Funk<PERSON> kann mit dem Schlüsselwort \"new\" aufgerufen werden.", "Only_ambient_modules_can_use_quoted_names_1035": "Nur Umgebungsmodule dürfen Namen in Anführungszeichen verwenden.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Nur die Module \"amd\" und \"system\" werden in Verbindung mit --{0} unterstützt.", "Only_emit_d_ts_declaration_files_6014": "Geben Sie nur .d.ts-Deklarationsdateien aus.", "Only_named_exports_may_use_export_type_1383": "\"export type\" kann nur von benannten Exporten verwendet werden.", "Only_numeric_enums_can_have_computed_members_but_this_expression_has_type_0_If_you_do_not_need_exhau_18033": "Nur numerische Enumerationen können berechnete Member umfassen, aber dieser Ausdruck weist den Typ \"{0}\" auf. Wenn Sie keine Vollständigkeitsprüfung benötigen, erwägen Sie stattdessen die Verwendung eines Objektliterals.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Nur d.ts-<PERSON><PERSON> und keine JavaScript-Dateien ausgeben.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Nur auf öffentliche und geschützte Methoden der Basisklasse kann über das Schlüsselwort \"super\" zugegriffen werden.", "Operator_0_cannot_be_applied_to_type_1_2736": "Der Operator \"{0}\" kann nicht auf den Typ \"{1}\" angewendet werden.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Der Operator \"{0}\" darf nicht auf die Typen \"{1}\" und \"{2}\" angewendet werden.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Deaktivieren Sie bei der Bearbeitung ein Projekt von der Überprüfung mehrerer Projektverweise.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "Die Option \"{0}\" kann nur in der Datei \"tsconfig.json\" angegeben oder in der Befehlszeile auf FALSE oder NULL festgelegt werden.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "Die Option \"{0}\" kann nur in der Datei \"tsconfig.json\" angegeben oder in der Befehlszeile auf NULL festgelegt werden.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "Die Option \"{0}\" kann nur verwendet werden, wenn die Option \"-inlineSourceMap\" oder \"-sourceMap\" angegeben wird.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "Die Option \"{0}\" kann nicht angegeben werden, wenn die Option \"jsx\" den Wert \"{1}\" aufweist.", "Option_0_cannot_be_specified_when_option_target_is_ES3_5048": "Die Option \"{0}\" kann nicht angegeben werden, wenn die Option \"target\" den Wert \"ES3\" aufweist.", "Option_0_cannot_be_specified_with_option_1_5053": "Die Option \"{0}\" darf nicht zusammen mit der Option \"{1}\" angegeben werden.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "Die Option \"{0}\" darf nicht ohne die Option \"{1}\" angegeben werden.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "Die Option \"{0}\" kann nicht ohne die Option \"{1}\" oder \"{2}\" angegeben werden.", "Option_build_must_be_the_first_command_line_argument_6369": "Die Option \"--build\" muss das erste Befehlszeilenargument sein.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "Die Option \"--incremental\" kann nur mit \"tsconfig\" und bei Ausgabe in eine einzelne Datei oder bei Festlegung der Option \"--tsBuildInfoFile\" angegeben werden.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "Die Option \"isolatedModules\" kann nur verwendet werden, wenn entweder die Option \"--module\" angegeben ist oder die Option \"target\" den Wert \"ES2015\" oder höher aufweist.", "Option_preserveConstEnums_cannot_be_disabled_when_isolatedModules_is_enabled_5091": "Die Option \"preserveConstEnums\" kann nicht deaktiviert werden, wenn \"isolatedModules\" aktiviert ist.", "Option_preserveValueImports_can_only_be_used_when_module_is_set_to_es2015_or_later_5095": "Die Option „preserveValueImports“ kann nur verwendet werden, wenn „module“ auf „es2015“ oder höher festgelegt ist.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Die Option \"project\" darf nicht mit Quelldateien in einer Befehlszeile kombiniert werden.", "Option_resolveJsonModule_can_only_be_specified_when_module_code_generation_is_commonjs_amd_es2015_or_5071": "Die Option \"--resolveJsonModule\" kann nur angegeben werden, wenn die Modulcodegenerierung \"commonjs\", \"amd\", \"es2015\" oder \"esNext\" lautet.", "Option_resolveJsonModule_cannot_be_specified_without_node_module_resolution_strategy_5070": "Die Option \"--resolveJsonModule\" kann nicht ohne die Modulauflösungsstrategie \"node\" angegeben werden.", "Options_0_and_1_cannot_be_combined_6370": "Die Optionen \"{0}\" und \"{1}\" können nicht kombiniert werden.", "Options_Colon_6027": "Optionen:", "Output_Formatting_6256": "Ausgabeformatierung", "Output_compiler_performance_information_after_building_6615": "Ausgabe Compiler-Leistungsinformationen nach dem Erstellen.", "Output_directory_for_generated_declaration_files_6166": "Ausgabeverzeichnis für erstellte Deklarationsdateien.", "Output_file_0_from_project_1_does_not_exist_6309": "Die Ausgabedatei \"{0}\" aus dem Projekt \"{1}\" ist nicht vorhanden.", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Die Ausgabedatei \"{0}\" wurde nicht aus der Quelldatei \"{1}\" erstellt.", "Output_from_referenced_project_0_included_because_1_specified_1411": "Ausgabe aus referenziertem Projekt \"{0}\" eingeschlossen, da \"{1}\" angegeben wurde", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "Ausgabe aus referenziertem Projekt \"{0}\" eingeschlossen, da \"--module\" als \"none\" angegeben wurde", "Output_more_detailed_compiler_performance_information_after_building_6632": "Geben Sie ausführlichere Compilerleistungsinformationen nach der Erstellung aus.", "Overload_0_of_1_2_gave_the_following_error_2772": "Die Überladung {0} von {1} ({2}) hat den folgenden Fehler verursacht.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Überladungssignaturen müssen alle abstrakt oder nicht abstrakt sein.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Überladungssignaturen müssen alle umgebend oder nicht umgebend sein.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Überladungssignaturen müssen alle exportiert oder nicht exportiert sein.", "Overload_signatures_must_all_be_optional_or_required_2386": "Überladungssignaturen müssen alle optional oder erforderlich sein.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Überladungssignaturen müssen alle öffentlich, privat oder geschützt sein.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Der Parameter \"{0}\" darf nicht auf den anschließend deklarierten Bezeichner \"{1}\" verweisen.", "Parameter_0_cannot_reference_itself_2372": "Der Parameter \"{0}\" kann nicht auf sich selbst verweisen.", "Parameter_0_implicitly_has_an_1_type_7006": "Der Parameter \"{0}\" weist implizit einen <PERSON>p \"{1}\" auf.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "Der Parameter \"{0}\" weist implizit einen Typ \"{1}\" auf, möglicherweise kann jedoch ein besserer Typ aus der Syntax abgeleitet werden.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Der Parameter \"{0}\" befindet sich nicht an der gleichen Position wie der Parameter \"{1}\".", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Der Parameter \"{0}\" des Accessors besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Der Parameter \"{0}\" des Accessors besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Der Parameter \"{0}\" des Accessors besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Der Parameter \"{0}\" der Aufrufsignatur aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Der Parameter \"{0}\" der Aufrufsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Der Parameter \"{0}\" des Konstruktors aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Der Parameter \"{0}\" des Konstruktors aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Der Parameter \"{0}\" des Konstruktors aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Der Parameter \"{0}\" der Konstruktorsignatur aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Der Parameter \"{0}\" der Konstruktorsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Der Parameter \"{0}\" der exportierten Funktion besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Der Parameter \"{0}\" der exportierten Funktion besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Der Parameter \"{0}\" der exportierten Funktion besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Der Parameter \"{0}\" der Indexsignatur aus der exportierten Schnittstelle weist den Namen \"{1}\" aus dem privaten Modul \"{2}\" auf oder verwendet diesen.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Der Parameter \"{0}\" der Indexsignatur aus der exportierten Schnittstelle weist den privaten Namen \"{1}\" auf oder verwendet diesen.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Der Parameter \"{0}\" der Methode aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Der Parameter \"{0}\" der Methode aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Der Parameter \"{0}\" der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Der Parameter \"{0}\" der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Der Parameter \"{0}\" der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Der Parameter \"{0}\" der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Der Parameter \"{0}\" der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Der Parameter \"{0}\" der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_cannot_have_question_mark_and_initializer_1015": "Der Parameter darf kein Fragezeichen und keinen Initialisierer aufweisen.", "Parameter_declaration_expected_1138": "Eine Parameterdeklaration wurde erwartet.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Der Parameter weist einen <PERSON>, aber keinen Typ auf. Meinten Sie \"{0}: {1}\"?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Parametermodifizierer können nur in TypeScript-Dateien verwendet werden.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Der Parametertyp des öffentlichen Setters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Der Parametertyp des öffentlichen Setters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Der Parametertyp des öffentlichen statischen Setters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Der Parametertyp des öffentlichen statischen Setters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Im Strict-Modus analysieren und \"use strict\" für jede Quelldatei ausgeben.", "Part_of_files_list_in_tsconfig_json_1409": "<PERSON><PERSON> der Liste \"files\" in tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "<PERSON> Muster \"{0}\" darf höchstens ein Zeichen \"*\" aufweisen.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Leistungsdaten zum zeitlichen Ablauf sind für \"--diagnostics\" oder \"--extendedDiagnostics\" in dieser Sitzung nicht verfügbar. Eine native Implementierung der Webleistungs-API wurde nicht gefunden.", "Platform_specific_6912": "Plattformspezifisch", "Prefix_0_with_an_underscore_90025": "\"{0}\" einen <PERSON> v<PERSON>", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Verwenden Sie für alle falschen Eigenschaftendeklarationen das Präfix \"declare\".", "Prefix_all_unused_declarations_with_where_possible_95025": "Alle nicht verwendeten Deklarationen nach Möglichkeit mit dem Präfix \"_\" versehen", "Prefix_with_declare_95094": "P<PERSON>ä<PERSON><PERSON> \"declare\" voranste<PERSON>", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Behalten Si<PERSON> nicht verwendete importierte Werte in der JavaScript-Ausgabe bei, die andernfalls entfernt werden würden.", "Print_all_of_the_files_read_during_the_compilation_6653": "<PERSON>ucken Si<PERSON> alle Dateien, die während der Kompilierung gelesen wurden.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Während der Kompilierung gelesene Date<PERSON> d<PERSON>cken, einsch<PERSON>ßlich der Gründe für ihre Aufnahme.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Hiermit werden die Namen der Dateien und der Grund dafür ausgegeben, dass die Dateien in der Kompilierung enthalten sind.", "Print_names_of_files_part_of_the_compilation_6155": "Drucknamen des Dateiteils der Kompilierung.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Hiermit werden Namen der Dateien ausgegeben, die Teil der Kompilierung sind. Anschließend wird die Verarbeitung beendet.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Drucknamen des generierten Dateiteils der Kompilierung.", "Print_the_compiler_s_version_6019": "Die Version des Compilers ausgeben.", "Print_the_final_configuration_instead_of_building_1350": "Hiermit wird anstelle eines Builds die endgültige Konfiguration ausgegeben.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Drucken Sie die Namen der ausgegebenen Dateien nach einer Kompilierung.", "Print_this_message_6017": "Diese Nachricht ausgeben.", "Private_accessor_was_defined_without_a_getter_2806": "Die private Zugriffsmethode wurde ohne Getter definiert.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Private Bezeichner sind in Variablendeklarationen nicht zulässig.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Private Bezeichner sind außerhalb des Textes von Klassen nicht zulässig.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Private Bezeichner sind nur in Klassentexten zulässig und dürfen nur als Teil einer Klassenmitgliedsdeklaration, eines Eigenschaftszugriffs oder auf der linken Seite eines in-Ausdrucks verwendet werden.", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Private Bezeichner sind nur verfügbar, wenn als Ziel ECMAScript 2015 oder höher verwendet wird.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Private Bezeichner können nicht als Parameter verwendet werden.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "<PERSON>ür einen Typparameter kann nicht auf den privaten oder geschützten Member \"{0}\" zugegriffen werden.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Projekt \"{0}\" kann nicht erstellt werden, weil die Abhängigkeit \"{1}\" <PERSON><PERSON> enthält.", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Das Projekt \"{0}\" kann nicht erstellt werden, weil die zugehörige Abhängigkeit \"{1}\" nicht erstellt wurde.", "Project_0_is_being_forcibly_rebuilt_6388": "Die Neuerstellung des Projekts \"{0}\" wird erzwungen.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "Das Projekt \"{0}\" ist veraltet, da die Buildinfodatei \"{1}\" angibt, dass einige der Änderungen nicht ausgegeben wurden.", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "Projekt \"{0}\" ist veraltet, weil die Abhängigkeit \"{1}\" veraltet ist.", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "Das Projekt \"{0}\" ist veraltet, weil die Ausgabe \"{1}\" älter ist als die Eingabe \"{2}\"", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "Projekt \"{0}\" ist veraltet, weil die Ausgabedatei \"{1}\" nicht vorhanden ist.", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "Das Projekt \"{0}\" ist veraltet, weil die Ausgabe für das Projekt mit Version {1} generiert wurde, die sich von der aktuellen Version {2} unterscheidet.", "Project_0_is_out_of_date_because_output_of_its_dependency_1_has_changed_6372": "Das Projekt \"{0}\" ist veraltet, weil sich die zugehörige Abhängigkeit \"{1}\" geändert hat.", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "Das Projekt \"{0}\" ist veraltet, weil beim Lesen der Datei \"{1}\" ein Fehler aufgetreten ist.", "Project_0_is_up_to_date_6361": "Projekt \"{0}\" ist auf dem neuesten Stand.", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "Projekt \"{0}\" ist auf dem neuesten Stand, weil die neueste Eingabe \"{1}\" älter ist als die Ausgabe \"{2}\".", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "Das Projekt \"{0}\" ist aktuell, muss jedoch Zeitstempel von Ausgabedateien aktualisieren, die älter als Eingabedateien sind.", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "Projekt \"{0}\" ist mit .d.ts-Dateien aus den zugehörigen Abhängigkeiten auf dem neuesten Stand.", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Projektverweise dürfen keinen kreisförmigen Graphen bilden. Zyklus erkannt: {0}", "Projects_6255": "Projekte", "Projects_in_this_build_Colon_0_6355": "Projekte in diesem Build: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Eigenschaften mit dem Accessormodifizierer sind nur für ECMAScript 2015 und höher verfügbar.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "Die Eigenschaft \"{0}\" darf keinen Initialisierer aufweisen, weil sie als abstrakt markiert ist.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "Die Eigenschaft \"{0}\" stammt aus einer Indexsignatur. Der Zugriff muss daher mit [\"{0}\"] erfolgen.", "Property_0_does_not_exist_on_type_1_2339": "Die Eigenschaft \"{0}\" ist für den Typ \"{1}\" nicht vorhanden.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "Die Eigenschaft \"{0}\" existiert nicht für Typ \"{1}\". Meinten Sie \"{2}\"?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "Die Eigenschaft \"{0}\" ist für den Typ \"{1}\" nicht vorhanden. Möchten Sie stattdessen auf den statischen Member \"{2}\" zugreifen?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "Die Eigenschaft \"{0}\" ist für den Typ \"{1}\" nicht vorhanden. Müssen Sie Ihre Zielbibliothek ändern? Ändern Sie die Compileroption \"lib\" in \"{2}\" oder höher.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "Die Eigenschaft \"{0}\" ist für den Typ \"{1}\" nicht vorhanden. Ändern Sie die Compileroption \"lib\" so, dass sie \"dom\" enthält.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "Die Eigenschaft \"{0}\" weist keinen Initialisierer auf und ist in einem statischen Klassenblock nicht definitiv zugewiesen.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "Die Eigenschaft \"{0}\" weist keinen Initialisierer auf und ist im Konstruktor nicht definitiv zugewiesen.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "Die Eigenschaft \"{0}\" weist implizit den Typ \"any\" auf, weil ihrem get-Accessor eine Parametertypanmerkung fehlt.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "Die Eigenschaft \"{0}\" weist implizit den Typ \"any\" auf, weil ihrem set-Accessor eine Parametertypanmerkung fehlt.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "Die Eigenschaft \"{0}\" weist implizit den Typ \"any\" auf, aber für den zugehörigen get-Accessor kann möglicherweise ein besserer Typ aus der Syntax abgeleitet werden.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "Die Eigenschaft \"{0}\" weist implizit den Typ \"any\" auf, aber für den zugehörigen set-Accessor kann möglicherweise ein besserer Typ aus der Syntax abgeleitet werden.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "Die Eigenschaft \"{0}\" im Typ \"{1}\" kann nicht der gleichen Eigenschaft in Basistyp \"{2}\" zugewiesen werden.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "Die Eigenschaft \"{0}\" im Typ \"{1}\" kann dem Typ \"{2}\" nicht zugewiesen werden.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "Die Eigenschaft \"{0}\" im Typ \"{1}\" verweist auf einen anderen Member, auf den nicht aus Typ \"{2}\" zugegriffen werden kann.", "Property_0_is_declared_but_its_value_is_never_read_6138": "Die Eigenschaft \"{0}\" ist deklariert, aber ihr Wert wird nie gelesen.", "Property_0_is_incompatible_with_index_signature_2530": "Die Eigenschaft \"{0}\" ist nicht mit der Indexsignatur kompatibel.", "Property_0_is_missing_in_type_1_2324": "Die Eigenschaft \"{0}\" fehlt im Typ \"{1}\".", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "Die Eigenschaft \"{0}\" fehlt im Typ \"{1}\", aber ist im Typ \"{2}\" erforderlich.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "Auf die Eigenschaft \"{0}\" kann außerhalb der Klasse \"{1}\" nicht zugegriffen werden, weil sie einen privaten Bezeichner aufweist.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "Die Eigenschaft \"{0}\" ist im Typ \"{1}\" optional, im Typ \"{2}\" aber erforderlich.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "Die Eigenschaft \"{0}\" ist privat. Auf sie kann nur innerhalb der Klasse \"{1}\" zugegriffen werden.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "Die Eigenschaft \"{0}\" ist im Typ \"{1}\" privat, im Typ \"{2}\" hingegen nicht.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "Die Eigenschaft \"{0}\" ist geschützt und nur über eine Instanz der Klasse \"{1}\" zugänglich. Dies ist eine Instanz der Klasse \"{2}\".", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "Die Eigenschaft \"{0}\" ist geschützt. Auf sie kann nur innerhalb der Klasse \"{1}\" und ihrer Unterklassen zugegriffen werden.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "Die Eigenschaft \"{0}\" ist geschützt, Typ \"{1}\" ist aber keine von \"{2}\" abgeleitete Klasse.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "Die Eigenschaft \"{0}\" ist im Typ \"{1}\" ges<PERSON><PERSON><PERSON><PERSON>, im Typ \"{2}\" aber öffentlich.", "Property_0_is_used_before_being_assigned_2565": "Die Eigenschaft \"{0}\" wird vor ihrer Zuweisung verwendet.", "Property_0_is_used_before_its_initialization_2729": "Die Eigenschaft \"{0}\" wird vor ihrer Initialisierung verwendet.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "Die Eigenschaft \"{0}\" ist für den Typ \"{1}\" möglicherweise nicht vorhanden. Meinten Sie \"{2}\"?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "Die Eigenschaft \"{0}\" des JSX-Verteilungsattributs kann nicht der Zieleigenschaft zugewiesen werden.", "Property_0_of_exported_class_expression_may_not_be_private_or_protected_4094": "Die Eigenschaft \"{0}\" des exportierten Klassenausdrucks ist unter Umständen nicht privat oder geschützt.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "Die Eigenschaft \"{0}\" der exportierten Schnittstelle besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "Die Eigenschaft \"{0}\" der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "Die Eigenschaft \"{0}\" von <PERSON> \"{1}\" kann nicht \"{2}\" Indextyp \"{3}\" zugewiesen werden.", "Property_0_was_also_declared_here_2733": "Die Eigenschaft \"{0}\" wurde hier ebenfalls deklariert.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "Die Eigenschaft \"{0}\" überschreibt die Basiseigenschaft in \"{1}\". <PERSON><PERSON> dies beabsichtigt ist, fügen Sie einen Initialisierer hinzu. Andernfalls fügen Sie einen declare-Modifizierer hinzu, oder entfernen Sie die redundante Deklaration.", "Property_assignment_expected_1136": "Die Zuweisung einer Eigenschaft wurde erwartet.", "Property_destructuring_pattern_expected_1180": "Ein Eigenschaftendestrukturierungsmuster wurde erwartet.", "Property_or_signature_expected_1131": "Eine Eigenschaft oder Signatur wurde erwartet.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Der Eigenschaftswert kann nur ein Zeichenfolgenliteral, ein numerisches Literal, \"true\", \"false\", \"NULL\", ein Objektliteral oder ein Arrayliteral sein.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_or_ES3_6179": "Bieten Sie vollständige Unterstützung für Iterablen in \"for-of\", Verteilung und Destrukturierung mit dem Ziel \"ES5\" oder \"ES3\".", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Die öffentliche Methode \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Die öffentliche Methode \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Die öffentliche Methode \"{0}\" der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "Die öffentliche Eigenschaft \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "Die öffentliche Eigenschaft \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "Die öffentliche Eigenschaft \"{0}\" der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Die öffentliche statische Methode \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Die öffentliche statische Methode \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Die öffentliche statische Methode \"{0}\" der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "Die öffentliche statische Eigenschaft \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "Die öffentliche statische Eigenschaft \"{0}\" der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "Die öffentliche statische Eigenschaft \"{0}\" der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "Der qualifizierte Name \"{0}\" ist ohne Voranstellung von \"@param {object} {1}\" nicht zulässig.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "<PERSON><PERSON><PERSON> einen <PERSON>hler aus, wenn ein Funktionsparameter nicht gelesen wird.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Fehler für Ausdrücke und Deklarationen mit einem impliziten any-Typ auslösen.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "<PERSON><PERSON> für \"this\"-Ausdrücke mit einem impliziten any-<PERSON><PERSON> auslösen.", "Re_exporting_a_type_when_the_isolatedModules_flag_is_provided_requires_using_export_type_1205": "Das erneute Exportieren eines Typs erfordert bei Festlegung des Flags \"--isolatedModules\" die Verwendung von \"export type\".", "Redirect_output_structure_to_the_directory_6006": "Die Ausgabestruktur in das Verzeichnis umleiten.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Verringern Sie die Anzahl der Projekte, die von TypeScript automatisch geladen werden.", "Referenced_project_0_may_not_disable_emit_6310": "<PERSON><PERSON> referenzierten Projekt \"{0}\" darf nicht die Ausgabe deaktiviert werden.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Das referenzierte Projekt \"{0}\" muss für die Einstellung \"composite\" den Wert TRUE aufweisen.", "Referenced_via_0_from_file_1_1400": "Referenziert über \"{0}\" aus der Datei \"{1}\"", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2834": "Relative Importpfade erfordern explizite Dateierweiterungen in EcmaScript-Importen, wenn „--moduleResolution“ „node16“ oder „nodenext“ ist. Erwägen Sie, dem Importpfad eine Erweiterung hinzuzufügen.", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2835": "Relative Importpfade erfordern explizite Dateierweiterungen in EcmaScript-Importen, wenn „--moduleResolution“ „node16“ oder „nodenext“ ist. Hast du gemeint '{0}'?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Entfernen Sie eine Liste von Verzeichnissen aus dem Überwachungsvorgang.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Entfernen Sie eine Liste von Dateien aus der Verarbeitung des Überwachungsmodus.", "Remove_all_unnecessary_override_modifiers_95163": "Alle nicht benötigten override-Modifizierer entfernen", "Remove_all_unnecessary_uses_of_await_95087": "Alle nicht benötigten Verwendungen von \"await\" entfernen", "Remove_all_unreachable_code_95051": "Gesamten nicht erreichbaren Code entfernen", "Remove_all_unused_labels_95054": "Alle nicht verwendeten Bezeichnungen entfernen", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Entfernen Sie die geschweiften Klammern aus dem Text aller Pfeilfunktionen mit entsprechenden Problemen.", "Remove_braces_from_arrow_function_95060": "Geschweifte Klammern aus Pfeilfunktion entfernen", "Remove_braces_from_arrow_function_body_95112": "Geschweifte Klammern aus Pfeilfunktionstext entfernen", "Remove_import_from_0_90005": "Import aus \"{0}\" entfernen", "Remove_override_modifier_95161": "override-Modifizierer entfernen", "Remove_parentheses_95126": "Klammern entfernen", "Remove_template_tag_90011": "Vorlagentag entfernen", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Entfernen Sie die Obergrenze von 20 MB für die Gesamtgröße des Quellcodes für JavaScript-Dateien auf dem TypeScript-Sprachserver.", "Remove_type_from_import_declaration_from_0_90055": "„type“ aus Importdeklaration aus „{0}“ entfernen", "Remove_type_from_import_of_0_from_1_90056": "„type“ aus Import von „{0}“ aus „{1}“ entfernen", "Remove_type_parameters_90012": "Typparameter entfernen", "Remove_unnecessary_await_95086": "<PERSON>nö<PERSON><PERSON> Vorkommen von \"await\" entfernen", "Remove_unreachable_code_95050": "Nicht erreichbaren Code entfernen", "Remove_unused_declaration_for_Colon_0_90004": "Nicht verwendete Deklaration für \"{0}\" entfernen", "Remove_unused_declarations_for_Colon_0_90041": "Nicht verwendete Deklarationen für \"{0}\" entfernen", "Remove_unused_destructuring_declaration_90039": "Nicht verwendete Destrukturierungsdeklaration entfernen", "Remove_unused_label_95053": "Nicht verwendete Bezeichnung entfernen", "Remove_variable_statement_90010": "Variablenanweisung entfernen", "Rename_param_tag_name_0_to_1_95173": "Tagnamen \"@param\" \"{0}\" in \"{1}\" umbenennen", "Replace_0_with_Promise_1_90036": "\"{0}\" durch \"Promise<{1}>\" ersetzen", "Replace_all_unused_infer_with_unknown_90031": "Alle nicht verwendeten Vorkommen von \"infer\" durch \"unknown\" ersetzen", "Replace_import_with_0_95015": "Ersetzen Sie den Import durch \"{0}\".", "Replace_infer_0_with_unknown_90030": "\"infer {0}\" durch \"unknown\" ersetzen", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "<PERSON><PERSON> melden, wenn nicht alle Codepfade in der Funktion einen Wert zurückgeben.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "<PERSON><PERSON><PERSON> FallTrough-<PERSON><PERSON><PERSON> in switch-Anweisung Fehler melden.", "Report_errors_in_js_files_8019": "<PERSON><PERSON> in .js-<PERSON><PERSON> me<PERSON>.", "Report_errors_on_unused_locals_6134": "Fehler für nicht verwendete lokale Variablen melden.", "Report_errors_on_unused_parameters_6135": "<PERSON>hler für nicht verwendete Parameter melden.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "Nicht deklarierte Eigenschaften aus Indexsignaturen müssen Elementzugriffe verwenden.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Erforderliche Typparameter dürfen nicht auf optionale Typparameter folgen.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "Die Auflösung für das Modul \"{0}\" wurde im Cache des Standorts \"{1}\" gefunden.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "Die Auflösung für die Typreferenzanweisung \"{0}\" wurde im Cache des Standorts \"{1}\" gefunden.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "\"keyof\" darf nur in Eigenschaftennamen mit Zeichenfolgenwert aufgelöst werden (keine Ziffern oder Symbole).", "Resolving_in_0_mode_with_conditions_1_6402": "Wird im {0}-Modus mit Bedingungen \"{1}\" aufgelöst.", "Resolving_module_0_from_1_6086": "======== Das Modul \"{0}\" aus \"{1}\" wird aufgelöst. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Der Modulname \"{0}\" relativ zur Basis-URL \"{1}\"–\"{2}\" wird aufgelöst.", "Resolving_real_path_for_0_result_1_6130": "Der tatsächliche Pfad für \"{0}\" wird aufgelöst, Ergeb<PERSON> \"{1}\".", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Die Typverweisdirektive \"{0}\" wird auf<PERSON>, die die Datei \"{1}\" enthält. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Die Typverweisdirektive \"{0}\" wird auf<PERSON>, die die Datei \"{1}\" enthält. Das Stammverzeichnis ist \"{2}\". ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Die Typverweisdirektive \"{0}\" wird auf<PERSON>, die die Datei \"{1}\" enthält. Das Stammverzeichnis ist nicht festgelegt. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Die Typverweisdirektive \"{0}\" wird auf<PERSON>, die die nicht festgelegte Datei enthält. Das Stammverzeichnis ist \"{1}\". ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Die Typverweisdirektive \"{0}\" wird aufgel<PERSON>, die die nicht festgelegte Datei enthält. Das Stammverzeichnis ist nicht festgelegt. ========", "Resolving_with_primary_search_path_0_6121": "Die Auflösung erfolgt mit dem primären Suchpfad \"{0}\".", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Der rest-parameter \"{0}\" weist implizit einen <PERSON> \"any[]\" auf.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Der rest-Parameter \"{0}\" weist implizit einen <PERSON>p \"any[]\" auf, möglicherweise kann jedoch ein besserer Typ aus der Syntax abgeleitet werden.", "Rest_types_may_only_be_created_from_object_types_2700": "Rest-Typen dürfen nur aus object-Typen erstellt werden.", "Return_type_annotation_circularly_references_itself_2577": "Die Rückgabetypanmerkung verweist zirkulär auf sich selbst.", "Return_type_must_be_inferred_from_a_function_95149": "Der Rückgabetyp muss aus einer Funktion abgeleitet werden.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Der Rückgabetyp der Aufrufsignatur aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Der Rückgabetyp der Aufrufsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{0}\".", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Der Rückgabetyp der Konstruktorsignatur aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Der Rückgabetyp der Konstruktorsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{0}\".", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Der Rückgabetyp der Konstruktorsignatur muss dem Instanztyp der Klasse zugewiesen werden können.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Der Rückgabetyp der exportierten Funktion besitzt oder verwendet den Namen \"{0}\" aus dem externen Modul \"{1}\", kann aber nicht benannt werden.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Der Rückgabetyp der exportierten Funktion besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Der Rückgabetyp der exportierten Funktion besitzt oder verwendet den privaten Namen \"{0}\".", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Der Rückgabetyp der Indexsignatur aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Der Rückgabetyp der Indexsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{0}\".", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Der Rückgabetyp der Methode aus der exportierten Schnittstelle besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Der Rückgabetyp der Methode aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{0}\".", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Der Rückgabetyp des öffentlichen Getters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Der Rückgabetyp des öffentlichen Getters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Der Rückgabetyp des öffentlichen Getters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Der Rückgabetyp der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{0}\" aus dem externen Modul \"{1}\", kann aber nicht benannt werden.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Der Rückgabetyp der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Der Rückgabetyp der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{0}\".", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Der Rückgabetyp des öffentlichen statischen Getters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem externen Modul \"{2}\", kann aber nicht benannt werden.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Der Rückgabetyp des öffentlichen statischen Getters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den Namen \"{1}\" aus dem privaten Modul \"{2}\".", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Der Rückgabetyp des öffentlichen statischen Getters \"{0}\" aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Der Rückgabetyp der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{0}\" aus dem externen Modul \"{1}\", kann aber nicht benannt werden.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Der Rückgabetyp der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den Namen \"{0}\" aus dem privaten Modul \"{1}\".", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Der Rückgabetyp der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{0}\".", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "Die Auflösung des Moduls „{0}“ aus „{1}“ im Cache vom Speicherort „{2}“ wird wiederverwendet, sie wurde nicht aufgelöst.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "Die Auflösung des Moduls „{0}“ aus „{1}\" im Cache vom Speicherort „{2}“ wird wiederverwendet, sie wurde erfolgreich in „{3}“ aufgelöst.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "Die Auflösung des Moduls „{0}“ aus „{1}“ im Cache vom Speicherort „{2}“ wird wiederverwendet, sie wurde erfolgreich in „{3}“ mit der Paket-ID „{4}“ aufgelöst.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Die Auflösung des Moduls „{0}“ aus „{1}“ des alten Programms wird wiederverwendet, sie wurde nicht aufgelöst.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Die Auflösung des Moduls „{0}“ aus „{1}“ des alten Programms wird wiederverwendet, sie wurde erfolgreich in „{2}“ aufgelöst.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Die Auflösung des Moduls „{0}“ aus „{1}“ des alten Programms wird wiederverwendet, sie wurde erfolgreich in „{2}“ mit der Paket-ID „{3}“ aufgelöst.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "Die Auflösung der Typverweisdirektive „{0}“ aus „{1}“ im Cache vom Speicherort „{2}“ wird wiederverwendet, sie wurde nicht aufgelöst.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "Die Auflösung der Typverweisdirektive „{0}“ aus „{1}“ im Cache vom Speicherort „{2}“ wird wiederverwendet, sie wurde erfolgreich in „{3}“ aufgelöst.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "Die Auflösung der Typverweisdirektive „{0}“ aus „{1}“ im Cache vom Speicherort „{2}“ wird wiederverwendet, sie wurde erfolgreich in „{3}“ mit der Paket-ID „{4}“ aufgelöst.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Die Auflösung der Typverweisdirektive „{0}“ aus „{1}“ des alten Programms wird wiederverwendet, sie wurde nicht aufgelöst.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Die Auflösung der Typverweisdirektive „{0}“ aus „{1}“ des alten Programms wird wiederverwendet, sie wurde erfolgreich in „{2}“ aufgelöst.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Die Auflösung der Typverweisdirektive „{0}“ aus „{1}“ des alten Programms wird wiederverwendet, sie wurde erfolgreich in „{2}“ mit der Paket-ID „{3}“ aufgelöst.", "Rewrite_all_as_indexed_access_types_95034": "Alle als indizierte Zugriffstypen neu schreiben", "Rewrite_as_the_indexed_access_type_0_90026": "Als indizierten Zugriffstyp \"{0}\" neu schreiben", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Das Stammverzeichnis kann nicht ermittelt werden. Die primären Suchpfade werden übersprungen.", "Root_file_specified_for_compilation_1427": "<PERSON>ür die Kompilierung angegebene Stammdatei", "STRATEGY_6039": "STRATEGIE", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Speichern Sie die .tsbuildinfo-Dateien, um eine inkrementelle Kompilierung von Projekten zuzulassen.", "Saw_non_matching_condition_0_6405": "Die nicht übereinstimmende Bedingung \"{0}\" wurde angezeigt.", "Scoped_package_detected_looking_in_0_6182": "Bereichsbezogenes Paket erkannt. In \"{0}\" wird gesucht", "Selection_is_not_a_valid_statement_or_statements_95155": "Die Auswahl umfasst keine gültigen Anweisungen.", "Selection_is_not_a_valid_type_node_95133": "Die Auswahl ist kein gültiger Typknoten.", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Legen Sie die JavaScript-Sprachversion für das ausgegebene JavaScript fest, und schließen Sie kompatible Bibliotheksdeklarationen ein.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Legen Sie die Sprache des Messagings von TypeScript fest. Dies wirkt sich nicht auf die Ausgabe aus.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Legen Sie die Option \"module\" in Ihrer Konfigurationsdatei auf \"{0}\" fest.", "Set_the_newline_character_for_emitting_files_6659": "Legen Sie das Zeilenumbruchzeichen für Ausgabedateien fest.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Legen Sie die Option \"target\" in Ihrer Konfigurationsdatei auf \"{0}\" fest.", "Setters_cannot_return_a_value_2408": "Setter können keinen Wert zurückgeben.", "Show_all_compiler_options_6169": "Alle Compileroptionen anzeigen.", "Show_diagnostic_information_6149": "Diagnoseinformationen anzeigen.", "Show_verbose_diagnostic_information_6150": "Ausführliche Diagnoseinformationen anzeigen.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "<PERSON><PERSON><PERSON><PERSON>, was erste<PERSON>t würde (oder gelöscht würde, wenn mit \"--clean\" angegeben)", "Signature_0_must_be_a_type_predicate_1224": "Die Signatur \"{0}\" muss ein Typprädikat sein.", "Skip_type_checking_all_d_ts_files_6693": "Überspringen Sie die Typüberprüfung aller .d.ts-Dateien.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Überspringen Sie die Typüberprüfung von .d.ts-Date<PERSON>, die in TypeScript enthalten sind.", "Skip_type_checking_of_declaration_files_6012": "Überspringen Sie die Typüberprüfung von Deklarationsdateien.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "<PERSON> Erstellen von Projekt \"{0}\" wird übersprungen, weil die Abhängigkeit \"{1}\" einen Fehler aufweist.", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Das Kompilieren von Projekt \"{0}\" wird übersprungen, weil die Abhängigkeit \"{1}\" nicht erstellt wurde.", "Source_from_referenced_project_0_included_because_1_specified_1414": "Quelle aus referenziertem Projekt \"{0}\", da \"{1}\" angegeben wurde", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "Quelle aus referenziertem Projekt \"{0}\", da \"--module\" als \"none\" angegeben wurde", "Source_has_0_element_s_but_target_allows_only_1_2619": "Die Quelle weist {0} <PERSON><PERSON>(e) auf, aber das Ziel lässt nur {1} zu.", "Source_has_0_element_s_but_target_requires_1_2618": "Die Quelle weist {0} <PERSON><PERSON>(e) auf, aber das Ziel erfordert {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "Die Quelle weist keine Übereinstimmung für das erforderliche Element an Position {0} im Ziel auf.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "Die Quelle weist keine Übereinstimmung für das variadic-Element an Position {0} im Ziel auf.", "Specify_ECMAScript_target_version_6015": "Geben Sie die ECMAScript-Zielversion an.", "Specify_JSX_code_generation_6080": "Geben Sie die JSX-Codegenerierung an.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Geben Sie eine Datei an, die alle Ausgaben in einer JavaScript-Datei bündelt. Wenn „declaration“ TRUE ist, wird auch eine Datei festgelegt, die alle „.d.ts“-Ausgaben bündelt.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "<PERSON>eben Sie eine Liste von Globmustern an, die mit Dateien übereinstimmen, die in die Kompilierung einbezogen werden sollen.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Geben Sie eine Liste der einzuschließenden Sprachdienst-Plug-Ins an.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Geb<PERSON> Si<PERSON> einen Satz gebündelter Bibliotheksdeklarationsdateien an, die die Ziellaufzeitumgebung beschreiben.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "<PERSON><PERSON><PERSON> Si<PERSON> einen Satz von Einträgen an, die Importe an zusätzliche Lookup-Speicherorte neu zuordnen.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "<PERSON>eb<PERSON> Si<PERSON> ein Objektarray an, das Pfade für Projekte angibt. Wird in Projekt verweisen verwendet.", "Specify_an_output_folder_for_all_emitted_files_6678": "Geben Sie einen Ausgabeordner für alle ausgegebenen Dateien an.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Geben Sie das Ausgabe-/Überprüfungsverhalten für Importe an, die nur für Typen verwendet werden.", "Specify_file_to_store_incremental_compilation_information_6380": "Datei zum Speichern inkrementeller Kompilierungsinformationen angeben", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Geben Si<PERSON> an, wie TypeScript eine Datei aus einem angegebenen Modulspezifizierer sucht.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Geben Si<PERSON> an, wie Verzeichnisse auf Systemen überwacht werden, für die eine rekursive Dateiüberwachungsfunktion fehlt.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Geb<PERSON>, wie der TypeScript-Überwachungsmodus funktioniert.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Geben Sie Bibliotheksdateien an, die in die Kompilierung eingeschlossen werden sollen.", "Specify_module_code_generation_6016": "Geben Sie die Modulcodegenerierung an.", "Specify_module_resolution_strategy_Colon_node_Node_js_or_classic_TypeScript_pre_1_6_6069": "Geben Sie die Modulauflösungsstrategie an: \"node\" (Node.js) oder \"classic\" (TypeScript vor Version 1.6).", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Geben Sie den Modulspezifizierer an, der zum Importieren der JSX-Factoryfunktionen verwendet wird, wenn <PERSON>e „jsx: react-jsx*“ verwenden.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Geben Sie mehrere Ordner an, die als „./node_modules/@types“ fungieren.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Geben Si<PERSON> einen oder mehrere Pfad- oder Knotenmodulverweise auf Basiskonfigurationsdateien an, von denen Einstellungen geerbt werden.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Geben Sie Optionen für den automatischen Erwerb von Deklarationsdateien an.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Geben Sie die Strategie zum Erstellen einer Abrufüberwachung an, wenn eine Erstellung mit Dateisystemereignissen nicht erfolgreich ist: \"FixedInterval\" (Standardwert), \"PriorityInterval\", \"DynamicPriority\", \"FixedChunkSize\".", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Geben Sie die Strategie für die Verzeichnisüberwachung auf Plattformen an, die eine rekursive Überwachung nativ nicht unterstützen: \"UseFsEvents\" (Standardwert), \"FixedPollingInterval\", \"DynamicPriorityPolling\", \"FixedChunkSizePolling\".", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Geben Sie die Strategie für die Dateiüberwachung an: \"FixedPollingInterval\" (Standardwert), \"PriorityPollingInterval\", \"DynamicPriorityPolling\", \"FixedChunkSizePolling\", \"UseFsEvents\", \"UseFsEventsOnParentDirectory\".", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Geben Sie den JSX-Fragmentverweis an, der für Fragmente verwendet wird, wenn die React JSX-Ausgabe als Ziel verwendet wird, z. B. \"React.Fragment\" oder \"Fragment\".", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Geben Sie die JSX-Factoryfunktion an, die für eine react-JSX-Ausgabe verwendet werden soll, z. B. \"React.createElement\" oder \"h\".", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Geben Sie die JSX-Factoryfunktion an, die verwendet wird, wenn Sie die JSX-Ausgabe „react“ als Ziel verwenden, z. B. „React.createElement“ oder „h“.", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Geben Sie die jsxFragmentFactory-Funktion an, die bei Verwendung des JSX-Ausgabeziels \"react\" mit der Compileroption \"jsxFactory\" verwendet werden soll, z. B. \"Fragment\".", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Geben Sie das Basisverzeichnis zum Auflösen nicht relativer Modulnamen an.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Geben Sie die Zeilenendesequenz an, die beim Ausgeben von Dateien verwendet werden soll: \"CRLF\" (DOS) oder \"LF\" (Unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Geben Sie den Speicherort an, an dem der Debugger TypeScript-<PERSON><PERSON> ermitteln soll, anstatt Quellspeicherorte zu verwenden.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Geben Sie den Speicherort an, an dem der Debugger Zuordnungsdateien ermitteln soll, anstatt generierte Speicherorte zu verwenden.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Geb<PERSON> Sie die maximale Ordnertiefe an, die zum Überprüfen von JavaScript-Dateien aus „node_modules“ verwendet wird. Gilt nur für „allowJs“.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Geben Sie den Modulspezifizierer an, aus dem die Factoryfunktionen \"jsx\" und \"jsxs\" importiert werden sollen, z. B. \"react\".", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Geben Sie das Objekt an, das für „createElement“ aufgerufen wird. Dies gilt nur, wenn die JSX-Ausgabe „react“ als Ziel verwendet wird.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Geben Sie das Ausgabeverzeichnis für generierte Deklarationsdateien an.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Geben Sie den Pfad zu inkrementelle Kompilierungsdateien .tsbuildinfo an.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Geben Sie das Stammverzeichnis der Eingabedateien an. Verwenden Sie diese Angabe, um die Ausgabeverzeichnisstruktur mithilfe von \"-outDir\" zu steuern.", "Specify_the_root_folder_within_your_source_files_6690": "Geben Sie den Stammordner in den Quelldateien an.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Geben Sie den Stammpfad für Debugger an, um den Verweisquellcode zu suchen.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Geben Sie Typpaketnamen an, die eingeschlossen werden sollen, ohne in einer Quelldatei referenziert zu werden.", "Specify_what_JSX_code_is_generated_6646": "<PERSON><PERSON><PERSON> an, welcher JSX-Code generiert wird.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "<PERSON><PERSON><PERSON>, welchen Ansatz der Watcher verwenden soll, wenn auf dem System keine nativen Dateiüberwachungen mehr vorhanden sind.", "Specify_what_module_code_is_generated_6657": "<PERSON><PERSON>en Si<PERSON> an, welcher Modulcode generiert wird.", "Split_all_invalid_type_only_imports_1367": "Alle ungültigen reinen Typenimporte teilen", "Split_into_two_separate_import_declarations_1366": "In zwei separate Importdeklarationen teilen", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "Der Verteilungsoperator in new-Ausdrücken ist nur verfügbar, wenn das Ziel ECMAScript 5 oder höher ist.", "Spread_types_may_only_be_created_from_object_types_2698": "Spread-Typen dürfen nur aus object-Typen erstellt werden.", "Starting_compilation_in_watch_mode_6031": "Kompilierung im Überwachungsmodus wird gestartet...", "Statement_expected_1129": "Eine Anweisung wurde erwartet.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Anweisungen sind in Umgebungskontexten unzulässig.", "Static_members_cannot_reference_class_type_parameters_2302": "Statische Member dü<PERSON><PERSON> nicht auf Klassentypparameter verweisen.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "Die statische Eigenschaft \"{0}\" steht in Konflikt mit der integrierten Eigenschaft \"Function.{0}\" der Konstruktorfunktion \"{1}\".", "String_literal_expected_1141": "Ein Zeichenfolgenliteral wurde erwartet.", "String_literal_with_double_quotes_expected_1327": "Ein Zeichenfolgenliteral mit doppelten Anführungszeichen wird erwartet.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "<PERSON><PERSON> und Nachrichten farbig und mit Kontext formatieren (experimentell).", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Nachfolgende Eigenschaftendeklarationen müssen den gleichen Typ aufweisen. Die Eigenschaft \"{0}\" muss den Typ \"{1}\" aufweisen, ist hier aber vom Typ \"{2}\".", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Nachfolgende Variablendeklarationen müssen den gleichen Typ aufweisen. Die Variable \"{0}\" muss den Typ \"{1}\" aufweisen, ist hier aber vom Typ \"{2}\".", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Die Ersetzung \"{0}\" für das Muster \"{1}\" weist einen falschen Typ auf. Er<PERSON><PERSON> wurde \"string\", abger<PERSON><PERSON> wurde \"{2}\".", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "Die Ersetzung \"{0}\" im Muster \"{1}\" darf höchstens ein Zeichen \"*\" aufweisen.", "Substitutions_for_pattern_0_should_be_an_array_5063": "Die Ersetzung für das Muster \"{0}\" muss ein Array sein.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Ersetzungen für das Muster \"{0}\" dürfen kein leeres Array sein.", "Successfully_created_a_tsconfig_json_file_6071": "<PERSON><PERSON> \"tsconfig.json\" wurde erfolgreich erstellt.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "<PERSON><PERSON><PERSON><PERSON> von \"super\" sind außer<PERSON><PERSON> von Konstruktoren oder in geschachtelten Funktionen innerhalb von Konstruktoren unzulässig.", "Suppress_excess_property_checks_for_object_literals_6072": "Übermäßige Eigenschaftenüberprüfungen für Objektliterale unterdrücken.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "noImplicitAny-Fehler für die Indizierung von Objekten unterdrücken, denen Indexsignaturen fehlen.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Unterdrücken Sie „noImplicitAny“-Fehler beim Indizieren von Objekten ohne Indexsignaturen.", "Switch_each_misused_0_to_1_95138": "<PERSON><PERSON> falsch verwendete {0}-Element in \"{1}\" ändern", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Rufen Sie Rückrufe synchron auf, und aktualisieren Sie den Status von Verzeichnisüberwachungen auf Plattformen, die rekursive Überwachung nicht nativ unterstützen.", "Syntax_Colon_0_6023": "Syntax: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "Das Tag \"{0}\" erwartet mindestens {1} <PERSON><PERSON><PERSON><PERSON>, von der JSX-Factory \"{2}\" werden aber höchstens {3} bereitgestellt.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Mit Tags versehene Vorlagenausdrücke sind in einer optionalen Kette nicht zulässig.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "<PERSON> Ziel erlaubt nur {0} <PERSON><PERSON>(e), aber die Quelle kann mehr aufweisen.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "<PERSON> Ziel erfordert {0} <PERSON><PERSON>(e), aber die Quelle kann weniger aufweisen.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "Der Modifizierer \"{0}\" kann nur in TypeScript-Dateien verwendet werden.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "Der Operator \"{0}\" darf nicht den Typ \"symbol\" angewendet werden.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "Der Operator \"{0}\" ist für boolesche Typen unzulässig. Verwenden Sie stattdessen ggf. \"{1}\".", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "Die Eigenschaft \"{0}\" eines asynchronen Iterators muss eine Methode sein.", "The_0_property_of_an_iterator_must_be_a_method_2767": "Die Eigenschaft \"{0}\" eines Iterators muss eine Methode sein.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "<PERSON> \"Object\" kann nur wenigen anderen Typen zugewiesen werden. Wollten Sie stattdessen den Typ \"any\" verwenden?", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES3_and_ES5_Consider_using_a_stand_2496": "Auf das Objekt \"arguments\" darf in einer Pfeilfunktion in ES3 und ES5 nicht verwiesen werden. Verwenden Sie ggf. einen Standardfunktionsausdruck.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES3_and_ES5_Consider_usi_2522": "Auf das Objekt \"arguments\" darf in einer asynchronen Funktion oder Methode in ES3 und ES5 nicht verwiesen werden. Verwenden Sie ggf. eine Standardfunktion oder -methode.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "Der Text einer \"if\"-<PERSON><PERSON><PERSON><PERSON> kann keine leere Anweisung sein.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "Der Aufruf wäre für diese Implementierung erfolgreich, aber die Implementierungssignaturen von Überladungen sind nicht extern sichtbar.", "The_character_set_of_the_input_files_6163": "Der Zeichensatz der Eingabedateien.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "Die enthaltende Pfeilfunktion erfasst den globalen Wert von \"this\".", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "Der beinhaltende Funktions- oder Modulkörper ist zu groß für eine Ablaufsteuerungsanalyse.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Die aktuelle Datei ist ein CommonJS-Modul und kann „await“ nicht auf der obersten Ebene verwenden.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Die aktuelle Datei ist ein CommonJS-Modul, dessen Importe \"require\"-Aufrufe generieren. <PERSON> Datei, auf die verwiesen wird, ist jedoch ein ECMAScript-Modul und kann nicht mit \"require\" importiert werden. Erwägen Sie stattdessen, einen dynamischen 'import(\"{0}\")'-Auf<PERSON><PERSON> zu schreiben.", "The_current_host_does_not_support_the_0_option_5001": "Der aktuelle Host unterstützt die Option \"{0}\" nicht.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "Die Deklaration von \"{0}\", die Sie wahrscheinlich verwenden wollten, ist hier definiert.", "The_declaration_was_marked_as_deprecated_here_2798": "Die Deklaration wurde hier als veraltet markiert.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "Der erwartete Typ stammt aus der Eigenschaft \"{0}\", die hier für den Typ \"{1}\" deklariert wird.", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Der erwartete Typ stammt aus dem Rückgabetyp dieser Signatur.", "The_expected_type_comes_from_this_index_signature_6501": "Der erwartete Typ stammt aus dieser Indexsignatur.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "Der Ausdruck einer Exportzuweisung muss ein Bezeichner oder ein qualifizierter Name in einem Umgebungskontext sein.", "The_file_is_in_the_program_because_Colon_1430": "Die Datei befindet sich aus folgenden Gründen im Programm:", "The_files_list_in_config_file_0_is_empty_18002": "Die Liste \"files\" in der Konfigurationsdatei \"{0}\" ist leer.", "The_first_export_default_is_here_2752": "Der erste Exportstandard befindet sich hier.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Der erste Parameter der \"then\"-Methode einer Zusage muss ein Rückruf sein.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "Der globale Typ \"JSX.{0}\" darf nur eine Eigenschaft aufweisen.", "The_implementation_signature_is_declared_here_2750": "Die Implementierungssignatur wird hier deklariert.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "Die Meta-Eigenschaft „import.meta“ ist in Dateien, die in der CommonJS-Ausgabe erstellt werden, nicht zulässig.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "Die Meta-Eigenschaft „import.meta“ ist nur zulässig, wenn die Option „--module“ „es2020“, „es2022“, „esnext“, „system“, „node16“ oder „nodenext“ ist.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "Der abgeleitete Typ von \"{0}\" kann nicht ohne einen Verweis auf \"{1}\" benannt werden. Eine Portierung ist wahrscheinlich nicht möglich. Eine Typanmerkung ist erforderlich.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Der abgeleitete Typ von \"{0}\" verweist auf einen <PERSON>p mit zyklischer Struktur, die nicht trivial serialisiert werden kann. Es ist eine Typanmerkung erforderlich.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Der abgeleitete Typ von \"{0}\" verweist auf einen <PERSON>p \"{1}\", auf den nicht zugegriffen werden kann. Eine Typanmerkung ist erforderlich.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "Der abgeleitete Typ dieses Knotens überschreitet die maximale Länge, die vom Compiler serialisiert wird. Eine explizite Typanmerkung ist erforderlich.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "Die Schnittmenge \"{0}\" wurde auf \"niemals\" reduziert, weil die Eigenschaft \"{1}\" in mehreren Bestandteilen vorhanden und in einigen davon privat ist.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "Die Schnittmenge \"{0}\" wurde auf \"niemals\" reduziert, weil die Eigenschaft \"{1}\" in einigen Bestandteilen widersprüchliche Typen aufweist.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "Das Schlüsselwort \"intrinsic\" darf nur zum Deklarieren von vom Compiler bereitgestellten intrinsischen Typen verwendet werden.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "Um JSX-Fragmente mit der Compileroption \"jsxFactory\" zu verwenden, muss die Compileroption \"jsxFragmentFactory\" angegeben werden.", "The_last_overload_gave_the_following_error_2770": "Die letzte Überladung hat den folgenden Fehler verursacht.", "The_last_overload_is_declared_here_2771": "Die letzte Überladung wird hier deklariert.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "Die linke Seite einer for...in-Anweisung darf kein Destrukturierungsmuster sein.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "Die linke Seite einer for...in-Anweisung darf keine Typanmerkung verwenden.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "Die linke Seite einer for...in-Anweisung darf kein optionaler Eigenschaftenzugriff sein.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "Die linke Seite einer for...in-Anweisung muss eine Variable oder ein Eigenschaftenzugriff sein.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "Die linke Seite einer for...in-Anweisung muss vom Typ \"string\" oder \"any\" sein.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "Die linke Seite einer for...of-Anweisung darf keine Typanmerkung verwenden.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "Die linke Seite einer for...of-Anweisung darf kein optionaler Eigenschaftenzugriff sein.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "Die linke Seite einer „for...of“-Anweisung darf nicht „asynchron“ lauten.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "Die linke Seite einer for...of-Anweisung muss eine Variable oder ein Eigenschaftenzugriff sein.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "Die linke Seite einer arithmetischen Operation muss den Typ \"any\", \"number\" oder \"bigint\" aufweisen oder ein Enumerationstyp sein.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "Die linke Seite eines Zuweisungsausdrucks darf kein optionaler Eigenschaftenzugriff sein.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "Die linke Seite eines Zuweisungsausdrucks muss eine Variable oder ein Eigenschaftenzugriff sein.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "Die linke Seite eines instanceof-Ausdrucks muss den Typ \"any\" aufweisen oder ein Objekttyp bzw. ein Typparameter sein.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Das beim Anzeigen von Meldungen für den Benutzer verwendete Gebietsschema (z. B. \"de-de\").", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "Die maximale Abhängigkeitstiefe, die unter \"node_modules\" durchsucht und für die JavaScript-Dateien geladen werden sollen.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "Der Operand eines delete-Operators darf kein privater Bezeichner sein.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "Der Operand eines delete-Operators darf keine schreibgeschützte Eigenschaft sein.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "Der Operand eines delete-Operators muss ein Eigenschaftenverweis sein.", "The_operand_of_a_delete_operator_must_be_optional_2790": "Der Operand eines delete-Operators muss optional sein.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "Der Operand eines Inkrement- oder Dekrementoperators darf kein optionaler Eigenschaftenzugriff sein.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "Der Operand eines Inkrement- oder Dekrementoperators muss eine Variable oder ein Eigenschaftenzugriff sein.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "Der Parser hat ein ein entsprechendes Element \"{1}\" zu dem hier vorhandenen Token \"{0}\" erwartet.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "Der Projektstamm ist mehrdeutig, wird aber benötigt, um den Exportzuordnungseintrag „{0}“ in der Datei „{1}“ aufzulösen. Geben Sie die Compiler-Option „rootDir“ an, um die Mehrdeutigkeit aufzuheben.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "Der Projektstamm ist mehrdeutig, wird aber benötigt, um den Importzuordnungseintrag „{0}“ in der Datei „{1}“ aufzulösen. Geben Sie die Compiler-Option „rootDir“ an, um die Mehrdeutigkeit aufzuheben.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "Auf die Eigenschaft \"{0}\" kann für den Typ \"{1}\" nicht innerhalb dieser Klasse zugegriffen werden, weil sie von einem anderen privaten Bezeichner mit der gleichen Schreibweise verborgen wird.", "The_return_type_of_a_get_accessor_must_be_assignable_to_its_set_accessor_type_2380": "Der Rückgabetyp einer get-Zugriffsmethode muss dem zugehörigen set-Accessortyp zugewiesen werden können.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "<PERSON> Rückgabetyp einer Parameter-Decorator-<PERSON><PERSON> muss \"void\" oder \"any\" sein.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Der Rückgabetyp einer Eigenschaften-Decorator-Funktion muss \"void\" oder \"any\" sein.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Der Rückgabetyp einer asynchronen Funktion muss entweder eine gültige Zusage sein oder darf keinen aufrufbaren \"then\"-Member enthalten.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Der Rückgabetyp einer asynchronen Funktion oder Methode muss der globale Typ \"Promise<T>\" sein. Wollten Sie eigentlich \"Promise<{0}>\" verwenden?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "Die rechte Seite einer for...in-Anweisung muss den Typ \"any\" aufweisen oder ein Objekttyp bzw. ein Typparameter sein. Sie weist hier jedoch den Typ \"{0}\" auf.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "Die rechte Seite einer arithmetischen Operation muss den Typ \"any\", \"number\" oder \"bigint\" aufweisen oder ein Enumerationstyp sein.", "The_right_hand_side_of_an_instanceof_expression_must_be_of_type_any_or_of_a_type_assignable_to_the_F_2359": "Die rechte Seite eines instanceof-Ausdrucks muss den Typ \"any\" oder einen Ty<PERSON> aufweisen, der dem Schnittstellentyp \"Function\" zugewiesen werden kann.", "The_root_value_of_a_0_file_must_be_an_object_5092": "<PERSON> Stammwert einer {0}-<PERSON><PERSON> muss ein Objekt sein.", "The_shadowing_declaration_of_0_is_defined_here_18017": "Die verbergende Deklaration von \"{0}\" ist hier definiert.", "The_signature_0_of_1_is_deprecated_6387": "Die Signatur \"{0}\" von \"{1}\" ist veraltet.", "The_specified_path_does_not_exist_Colon_0_5058": "Der angegebene Pfad \"{0}\" ist nicht vorhanden.", "The_tag_was_first_specified_here_8034": "Das Tag wurde zuerst hier angegeben.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "Das Ziel einer rest-Zuweisung für ein Objekt darf kein optionaler Eigenschaftenzugriff sein.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "Das Ziel einer REST-Zuweisung für ein Objekt muss eine Variable oder ein Eigenschaftenzugriff sein.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "Der \"this\"-Kontext vom Typ \"{0}\" kann \"this\" vom Typ \"{1}\" der Methode nicht zugewiesen werden.", "The_this_types_of_each_signature_are_incompatible_2685": "Die \"this\"-Typen jeder Signatur sind nicht kompatibel.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "<PERSON> Typ \"{0}\" ist als \"readonly\" festgelegt und kann nicht dem änderbaren Typ \"{1}\" zugewiesen werden.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Der \"Type\"-Modifizierer kann nicht für einen benannten Export verwendet werden, wenn \"Export-Typ\" auf seiner Export-Anweisung verwendet wird.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "Der \"Type\"-Modifizierer kann nicht für einen benannten Import verwendet werden, wenn der \"Import-Typ\" auf seiner Import-Anweisung verwendet wird.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "Der Typ einer Funktionsdeklaration muss mit der Signatur der Funktion übereinstimmen.", "The_type_of_this_expression_cannot_be_named_without_a_resolution_mode_assertion_which_is_an_unstable_2841": "Der Typ dieses Ausdrucks kann nicht ohne eine „resolution-mode“-Assertion benannt werden, was eine instabile Funktion ist. Verwenden Sie nächtliches TypeScript, um diesen Fehler zu unterdrücken. Versuchen Sie, mit „npm install -D typescript@next“ zu aktualisieren.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "<PERSON> Typ dieses Knotens kann nicht serialisiert werden, da seine Eigenschaft \"{0}\" nicht serialisiert werden kann.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "<PERSON> von der {0}()-Methode eines Async-Iterators zurückgegebene Typ muss eine Zusage für einen Typ mit einer value-Eigenschaft sein.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "<PERSON> von der {0}()-Methode eines Iterators zurückgegebene Typ muss eine value-Eigenschaft aufweisen.", "The_types_of_0_are_incompatible_between_these_types_2200": "Die Typen von \"{0}\" sind zwischen diesen Typen nicht kompatibel.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "<PERSON> von \"{0}\" zurückgegebenen Typen sind zwischen diesen Typen nicht kompatibel.", "The_value_0_cannot_be_used_here_18050": "<PERSON> Wert \"{0}\" kann hier nicht verwendet werden.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "Die Variablendeklaration einer for...in-Anweisung darf keinen Initialisierer aufweisen.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "Die Variablendeklaration einer for...of-Anweisung darf keinen Initialisierer aufweisen.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "Die with-Anweisung wird nicht unterstützt. Alle Symbole in einem with-Block weisen den Typ \"any\" auf.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "Die Eigenschaft \"{0}\" für dieses JSX-Tag erwartet ein einzelnes untergeordnetes Element vom Typ \"{1}\", aber es wurden mehrere untergeordnete Elemente angegeben.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "Die Eigenschaft \"{0}\" für dieses JSX-Tag erwartet den Typ \"{1}\", der mehrere untergeordnete Elemente erfordert, aber es wurde nur ein untergeordnetes Elemente angegeben.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "<PERSON>ser Vergleich scheint unbeabsichtigt zu sein, da die Typen \"{0}\" und \"{1}\" keine Überlappung aufweisen.", "This_condition_will_always_return_0_2845": "Diese Bedingung gibt immer „{0}“ zurück.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Diese Bedingung gibt immer „{0}“ zurück, da JavaScript Objekte nach Verweis und nicht nach Wert vergleicht.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Diese Bedingung gibt immer TRUE zurück, weil diese '{0}' immer definiert ist.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Diese Bedingung gibt immer TRUE zurück, weil diese Funktion immer definiert ist. Möchten Sie sie stattdessen aufrufen?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Diese Konstruktorfunktion kann in eine Klassendeklaration konvertiert werden.", "This_expression_is_not_callable_2349": "Dieser Ausdruck kann nicht aufgerufen werden.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Dieser Ausdruck kann nicht aufgerufen werden, weil es sich um eine get-Zugriffsmethode handelt. Möchten Sie den Wert ohne \"()\" verwenden?", "This_expression_is_not_constructable_2351": "Dieser Ausdruck kann nicht erstellt werden.", "This_file_already_has_a_default_export_95130": "<PERSON><PERSON> weist bereits einen Standardexport auf.", "This_import_is_never_used_as_a_value_and_must_use_import_type_because_importsNotUsedAsValues_is_set__1371": "Dieser Import wird nie als Wert verwendet und muss \"import type\" verwenden, weil \"importsNotUsedAsValues\" auf \"error\" festgelegt ist.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "Dies ist die erweiterte Deklaration. Die erweiternde Deklaration sollte in dieselbe Datei verschoben werden.", "This_may_be_converted_to_an_async_function_80006": "Es kann eine Konvertierung in ein asynchrone Funktion durchgeführt werden.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Dieser Member kann keinen JSDoc-Kommentar mit einem \"@override\"-<PERSON> haben, da er nicht in der Basisklasse \"{0}\" deklariert ist.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "<PERSON><PERSON> kann keinen JSDoc-Kommentar mit einem Override-Tag haben, da er nicht in der Basisklasse \"{0}\" deklariert ist. Meinten Sie \"{1}\"?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "<PERSON><PERSON> kann keinen JSDoc-Kommentar mit einem Tag \"@override\" haben, da dessen enthaltende Klasse \"{0}\" keine andere Klasse erweitert.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Dieser Member kann keinen override-Modifizierer aufweisen, weil er nicht in der Basisklasse \"{0}\" deklariert ist.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Dieser Member kann keinen override-Modifizierer aufweisen, weil er nicht in der Basisklasse \"{0}\" deklariert ist. Meinten Sie \"{1}\"?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Dieser Member kann keinen override-Modifizierer aufweisen, weil die Klasse \"{0}\", die diesen Member enth<PERSON><PERSON>, keine andere Klasse erweitert.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "<PERSON><PERSON> Mi<PERSON> muss über einen JSDoc-Kommentar mit dem Tag \"@override\" ve<PERSON><PERSON><PERSON>, da er einen Member in der Basisklasse \"{0}\" überschreibt.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Dieser Member muss einen override-Modifizierer aufweisen, weil er einen Member in der Basisklasse \"{0}\" überschreibt.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Dieser Member muss einen override-Modifizierer aufweisen, weil er eine abstrakte Methode überschreibt, die in der Basisklasse \"{0}\" deklariert ist.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "<PERSON>f dieses Modul kann nur mit ECMAScript-Importen/-Exporten verwiesen werden, indem das Flag \"{0}\" aktiviert und auf den Standardexport verwiesen wird.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Die<PERSON> wird mit „export =“ deklariert und kann nur bei Verwendung des Kennzeichnens „{0}“ mit einem Standardimport verwendet werden.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Diese Überladungssignatur ist nicht mit der zugehörigen Implementierungssignatur kompatibel.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Dieser Parameter ist mit der Direktive \"use strict\" nicht zugelassen.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Diese Parametereigenschaft muss über einen JSDoc-Kommentar mit einem \"@override\"-<PERSON> verfügen, da sie ein Mitglied in der Basisklasse \"{0}\" überschreibt.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Diese Parametereigenschaft muss einen „override“-Modifizierer aufweisen, weil er einen Member in der Basisklasse \"{0}\" überschreibt.", "This_spread_always_overwrites_this_property_2785": "Diese Eigenschaft wird immer durch diesen Spread-Operator überschrieben.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Diese Syntax ist in Dateien mit der Erweiterung .mts oder .cts reserviert. Fügen Sie ein nachfolgendes Komma oder eine explizite Einschränkung hinzu.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Diese Syntax ist in Dateien mit der Erweiterung \".mts\" oder \".cts\" reserviert. Verwenden Sie stattdessen einen „as“-Ausdruck.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Diese Syntax erfordert ein importiertes Hilfsprogramm, aber das Modul \"{0}\" wurde nicht gefunden.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Diese Syntax erfordert ein importiertes Hilfsprogramm namens \"{1}\", das in \"{0}\" nicht vorhanden ist. Erwägen Sie ein Upgrade Ihrer Version von \"{0}\".", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Diese Syntax erfordert ein importiertes Hilfsprogramm mit dem Namen \"{1}\" mit {2} Parametern, die nicht mit der in \"{0}\" kompatibel ist. Erwägen Sie ein Upgrade Ihrer Version von \"{0}\".", "This_type_parameter_might_need_an_extends_0_constraint_2208": "<PERSON><PERSON><PERSON> diesen Typparameter ist möglicherweise die Einschränkung \"extends {0}\" erforderlich.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "<PERSON><PERSON> Verwendung von „import“ ist ungültig. „import()“-Aufrufe können geschrieben werden, müssen jedoch Klammern aufweisen und dürfen keine Typargumente aufweisen.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Um diese Datei in ein ECMAScript-<PERSON><PERSON><PERSON> zu konvertieren, fügen Si<PERSON> das Feld \"type\": \"module\" zu \"{0}\" hinzu.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Um diese Datei in ein ECMAScript-<PERSON><PERSON><PERSON> zu konvertieren, ändern Sie die Dateierweiterung in \"{0}\", oder fügen Sie das Feld ''type': 'module'' zu \"{1}\" hinzu.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Um diese Datei in ein ECMAScript-<PERSON><PERSON><PERSON> zu konvertieren, änder<PERSON> Sie ihre Dateierweiterung in '{0}', oder erstellen Sie eine lokale package.json-Datei mit `{ \"type\": \"module\" }`.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Um diese Datei in ein ECMAScript-<PERSON><PERSON><PERSON> zu konvertieren, erstellen Sie eine lokale package.json-Datei mit `{ \"type\": \"module\" }`.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "'await'-Ausdrücke der obersten Ebene sind nur zulässig, wenn die 'module'-Option auf 'es2022', 'esnext', 'system', 'node16' oder 'nodenext' und die 'target'-Option auf ' es2017' oder höher.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Deklarationen der obersten Ebene in .d.ts-<PERSON><PERSON> müssen entweder mit einem declare- oder einem export-Modifizierer beginnen.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "'for await'-Schleifen der obersten Ebene sind nur erlaubt, wenn die 'module'-Option auf 'es2022', 'esnext', 'system', 'node16' oder 'nodenext' gesetzt ist und die 'target'-Option auf gesetzt ist 'es2017' oder höher.", "Trailing_comma_not_allowed_1009": "Ein nachgestelltes Komma ist unzulässig.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "<PERSON><PERSON>i als separates Modul transpilieren (ähnlich wie bei \"ts.transpileModule\").", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Versuchen Sie es mit \"npm i --save-dev @types/{1}\", so<PERSON>n vor<PERSON>, oder fügen Sie eine neue Deklarationsdatei (.d.ts) hinzu, die \"declare module '{0}';\" enthält.", "Trying_other_entries_in_rootDirs_6110": "<PERSON><PERSON> Einträge in \"rootDirs\" werden versucht.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Die Ersetzung \"{0}\" wird versucht. Speicherort des Kandidatenmoduls: \"{1}\".", "Tuple_members_must_all_have_names_or_all_not_have_names_5084": "Von den Tupelelementen müssen entweder alle oder keines einen Namen aufweisen.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "Der Tupeltyp \"{0}\" der Länge {1} weist am Index \"{2}\" kein Element auf.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Tupeltypargumente verweisen zirkulär auf sich selbst.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "<PERSON> Typ \"{0}\" kann nur durchlaufen werden, wenn das Flag \"--downlevelIteration\" verwendet wird oder \"--target\" den Wert \"es2015\" oder höher aufweist.", "Type_0_cannot_be_used_as_an_index_type_2538": "<PERSON> \"{0}\" kann nicht als Indextyp verwendet werden.", "Type_0_cannot_be_used_to_index_type_1_2536": "<PERSON> \"{0}\" kann nicht zum Indizieren von <PERSON> \"{1}\" verwendet werden.", "Type_0_does_not_satisfy_the_constraint_1_2344": "<PERSON> Typ \"{0}\" erfüllt die Einschränkung \"{1}\" nicht.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "<PERSON> \"{0}\" erfüllt den erwarteten Typ \"{1}\" nicht.", "Type_0_has_no_call_signatures_2757": "<PERSON> \"{0}\" weist keine Aufrufsignaturen auf.", "Type_0_has_no_construct_signatures_2761": "<PERSON> Typ \"{0}\" weist keine Konstruktsignaturen auf.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "<PERSON> Typ \"{0}\" weist keine übereinstimmende Indexsignatur für den Typ \"{1}\" auf.", "Type_0_has_no_properties_in_common_with_type_1_2559": "<PERSON> Typ \"{0}\" verfügt über keine gemeinsamen Eigenschaften mit Typ \"{1}\".", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "<PERSON> „{0}“ weist keine Signaturen auf, für die die Liste „Typargument“ gilt.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "<PERSON><PERSON> \"{0}\" fehlen die folgenden Eigenschaften von <PERSON> \"{1}\": \"{2}\".", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "<PERSON><PERSON> \"{0}\" fehlen die folgenden Eigenschaften von <PERSON> \"{1}\": \"{2}\" und {3} weitere.", "Type_0_is_not_a_constructor_function_type_2507": "Der Typ \"{0}\" ist kein Konstruktorfunktionstyp.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_SlashES3_because_it_does_not_refer_to_a_Prom_1055": "Der Typ \"{0}\" ist in ES5/ES3 kein gültiger Rückgabetyp einer asynchronen Funktion, weil er nicht auf einen Promise-kompatiblen Konstruktorwert verweist.", "Type_0_is_not_an_array_type_2461": "<PERSON> Typ \"{0}\" ist kein Arraytyp.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Der Typ \"{0}\" ist kein Array- oder Zeichenfolgentyp.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "Typ \"{0}\" ist kein Array-Typ oder Zeichenfolgentyp oder weist keine \"[Symbol.iterator]()\"-<PERSON><PERSON> auf, die einen Iterator zurückgibt.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "<PERSON><PERSON> \"{0}\" ist kein Array-Typ oder weist keine \"[Symbol.iterator]()\"-<PERSON><PERSON> auf, die einen Iterator zurückgibt.", "Type_0_is_not_assignable_to_type_1_2322": "<PERSON> \"{0}\" kann dem Typ \"{1}\" nicht zugewiesen werden.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "<PERSON><PERSON> \"{0}\" kann dem Typ \"{1}\" nicht zugewiesen werden. Meinten Sie \"{2}\"?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "<PERSON> Ty<PERSON> \"{0}\" kann dem Typ \"{1}\" nicht zugewiesen werden. Es sind zwei verschiedene Typen mit diesem Namen vorhanden, diese sind jedoch nicht verwandt.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "<PERSON> „{0}“ kann dem Typ „{1}“ nicht zugewiesen werden, wie in der Abweichungsanmerkung impliziert.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "<PERSON> „{0}“ kann dem Typ „{1}“ mit „exactOptionalPropertyTypes: true“ nicht zugewiesen werden. Erwägen Sie das Hinzufügen von „undefined“ zu den Typen der Zieleigenschaften.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "<PERSON> „{0}“ kann dem Typ „{1}“ mit „exactOptionalPropertyTypes: true“ nicht zugewiesen werden. Erwägen Sie das Hinzufügen von „undefined“ zum Typ des Ziels.", "Type_0_is_not_comparable_to_type_1_2678": "<PERSON> \"{0}\" kann nicht mit dem <PERSON> \"{1}\" verglichen werden.", "Type_0_is_not_generic_2315": "<PERSON> \"{0}\" ist nicht generisch.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "Typ „{0}“ kann einen primitiven Wert da<PERSON>n, der als rechter Operand des „In“-Operators nicht zulässig ist.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "<PERSON> Typ \"{0}\" muss eine Methode \"[Symbol.asyncIterator]()\" auf<PERSON>sen, die einen async-Iterator zurückgibt.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "<PERSON> Typ \"{0}\" muss eine Methode \"[Symbol.iterator]()\" auf<PERSON><PERSON>, die einen Iterator zurückgibt.", "Type_0_provides_no_match_for_the_signature_1_2658": "<PERSON> Typ \"{0}\" enthält keine Entsprechung für die Signatur \"{1}\".", "Type_0_recursively_references_itself_as_a_base_type_2310": "<PERSON> Typ \"{0}\" verweist rekursiv auf sich selbst als ein Basistyp.", "Type_Checking_6248": "Typprüfung", "Type_alias_0_circularly_references_itself_2456": "Der Typalias \"{0}\" verweist zirkulär auf sich selbst.", "Type_alias_must_be_given_a_name_1439": "Typal<PERSON> muss einen Namen erhalten.", "Type_alias_name_cannot_be_0_2457": "<PERSON> Typaliasname darf nicht \"{0}\" sein.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "Typaliase können nur in TypeScript-Dateien verwendet werden.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Die Typanmerkung darf nicht für eine Konstruktordeklaration verwendet werden.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Typanmerkungen können nur in TypeScript-Dateien verwendet werden.", "Type_argument_expected_1140": "Ein Typargument wurde erwartet.", "Type_argument_list_cannot_be_empty_1099": "Die Typargumentliste darf nicht leer sein.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Typargumente können nur in TypeScript-Dateien verwendet werden.", "Type_arguments_cannot_be_used_here_1342": "Typargumente können an dieser Stelle nicht verwendet werden.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Typargumente für \"{0}\" verweisen zirkulär auf sich selbst.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Typassertionsausdrücke können nur in TypeScript-Dateien verwendet werden.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Der Typ an Position {0} in der Quelle ist nicht mit dem Typ an Position {1} im Ziel kompatibel.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Der Typ an den Positionen {0} bis {1} in der Quelle ist nicht mit dem Typ an Position {2} im Ziel kompatibel.", "Type_declaration_files_to_be_included_in_compilation_6124": "Typdeklarationsdateien, die in die Kompilierung eingeschlossen werden sollen.", "Type_expected_1110": "<PERSON>s wurde ein <PERSON> erwartet.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Typimportassertionen sollten über genau einen Schlüssel verfügen – „resolution-mode“ – mit dem Wert „import“ oder „require“.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "Die Typinstanziierung ist übermäßig tief und möglicherweise unendlich.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "<PERSON><PERSON> den Typ wird direkt oder indirekt im Erfüllungsrückruf der eigenen \"then\"-Methode verwiesen.", "Type_library_referenced_via_0_from_file_1_1402": "Typbibliothek, die über \"{0}\" aus der Datei \"{1}\" referenziert wird", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Typbibliothek, die über \"{0}\" aus der Datei \"{1}\" mit packageId \"{2}\" referenziert wird", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Der Typ des \"await\"-Operanden muss entweder eine gültige Zusage sein oder darf keinen aufrufbaren \"then\"-Member enthalten.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Der Typ des Werts der berechneten Eigenschaft lautet \"{0}\" und kann dem Typ \"{1}\" nicht zugewiesen werden.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "Der Typ der Instanzmembervariablen „{0}“ darf nicht auf den im Konstruktor deklarierten Bezeichner „{1}“ verweisen.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Der Typ iterierter Elemente eines \"yield*\"-Operanden muss entweder eine gültige Zusage sein oder darf keinen aufrufbaren \"then\"-Member enthalten.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "Der Typ der Eigenschaft \"{0}\" verweist im zugeordneten Typ \"{1}\" auf sich selbst.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Der Typ eines \"yield\"-Operanden in einem asynchronen Generator muss entweder eine gültige Zusage sein oder darf keinen aufrufbaren \"then\"-Member enthalten.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Der Typ stammt aus diesem Import. Ein Import im Namespacestil kann nicht aufgerufen oder erstellt werden und verursacht zur Laufzeit einen Fehler. Erwägen Sie hier stattdessen die Verwendung eines Standardimports oder die den Import über \"require\".", "Type_parameter_0_has_a_circular_constraint_2313": "Der Typparameter \"{0}\" weist eine zirkuläre Einschränkung auf.", "Type_parameter_0_has_a_circular_default_2716": "Der Typparameter \"{0}\" besitzt einen zirkulären Standardwert.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Der Typparameter \"{0}\" der Aufrufsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Der Typparameter \"{0}\" der Konstruktorsignatur aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Der Typparameter \"{0}\" der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Der Typparameter \"{0}\" der exportierten Funktion besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Der Typparameter \"{0}\" der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Der Typparameter \"{0}\" des exportierten zugeordneten Objekttyps verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Der Typparameter \"{0}\" des exportierten Typalias enthält oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Der Typparameter \"{0}\" der Methode aus der exportierten Schnittstelle besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Der Typparameter \"{0}\" der öffentlichen Methode aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Der Typparameter \"{0}\" der öffentlichen statischen Methode aus der exportierten Klasse besitzt oder verwendet den privaten Namen \"{1}\".", "Type_parameter_declaration_expected_1139": "Eine Typparameterdeklaration wurde erwartet.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Typparameterdeklarationen können nur in TypeScript-Dateien verwendet werden.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Standardwerte für Typparameter können nur auf zuvor deklarierte Typparameter verweisen.", "Type_parameter_list_cannot_be_empty_1098": "Die Typparameterliste darf nicht leer sein.", "Type_parameter_name_cannot_be_0_2368": "Der Name des Typparameters darf nicht \"{0}\" sein.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Typparameter dürfen nicht für eine Konstruktordeklaration verwendet werden.", "Type_predicate_0_is_not_assignable_to_1_1226": "Das Typprädikat \"{0}\" kann \"{1}\" nicht zugewiesen werden.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "Der Typ erzeugt einen Tupeltyp, der für die Darstellung zu groß ist.", "Type_reference_directive_0_was_not_resolved_6120": "======== Die Typverweisdirektive \"{0}\" wurde nicht aufgelöst. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== Die Typverweisdirektive \"{0}\" wurde erfolgreich in \"{1}\" aufgelöst. Primär: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== Die Typverweisdirektive \"{0}\" wurde erfolgreich in \"{1}\" mit Paket-ID \"{2}\" aufgelöst. Primär: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Typerfüllungsausdrücke können nur in TypeScript-Dateien verwendet werden.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "<PERSON>n können in Exportdeklarationen in JavaScript-Dateien nicht angezeigt werden.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Typen weisen separate Deklarationen einer privaten Eigenschaft \"{0}\" auf.", "Types_of_construct_signatures_are_incompatible_2419": "Die Typen der Konstruktsignaturen sind nicht kompatibel.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Die Typen der Parameter \"{0}\" und \"{1}\" sind nicht kompatibel.", "Types_of_property_0_are_incompatible_2326": "Die Typen der Eigenschaft \"{0}\" sind nicht kompatibel.", "Unable_to_open_file_0_6050": "<PERSON> Datei \"{0}\" kann nicht geöffnet werden.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Die Signatur des Klassen-Decorator-Elements kann nicht aufgelöst werden, wenn der Aufruf als Ausdruck erfolgt.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Die Signatur des Methoden-Decorator-Elements kann nicht aufgelöst werden, wenn der Aufruf als Ausdruck erfolgt.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Die Signatur des Parameter-Decorator-Elements kann nicht aufgelöst werden, wenn der Aufruf als Ausdruck erfolgt.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Die Signatur des Eigenschaften-Decorator-Elements kann nicht aufgelöst werden, wenn der Aufruf als Ausdruck erfolgt.", "Unexpected_end_of_text_1126": "Unerwartetes Textende.", "Unexpected_keyword_or_identifier_1434": "Unerwartetes Schlüsselwort oder Bezeichner.", "Unexpected_token_1012": "Unerwartetes <PERSON>.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Unerwartetes Token. Ein Konstruktor, eine Methode, eine Zugriffsmethode oder eine Eigenschaft wurde erwartet.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Unerwartetes Token. Es wurde ein Typparametername ohne geschweifte Klammern erwartet.", "Unexpected_token_Did_you_mean_or_gt_1382": "Unerwartetes Token. Meinten Sie \"{'>'}\" oder \"&gt;\"?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Unerwartetes Token. Meinten Sie \"{'}'}\" oder \"&rbrace;\"?", "Unexpected_token_expected_1179": "Unerwartetes To<PERSON>. \"{\" wurde erwartet.", "Unknown_build_option_0_5072": "Unbekannte Buildoption \"{0}\".", "Unknown_build_option_0_Did_you_mean_1_5077": "Unbekannte Buildoption \"{0}\". Meinten Sie \"{1}\"?", "Unknown_compiler_option_0_5023": "Unbekannte Compileroption \"{0}\".", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Unbekannte Compileroption \"{0}\". Meinten Sie \"{1}\"?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Unbekanntes Schlüsselwort oder Bezeichner. Meinten Sie \"{0}\"?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Unbekannte Option \"exclude\". <PERSON><PERSON><PERSON> Si<PERSON> \"exclude\"?", "Unknown_type_acquisition_option_0_17010": "Unbekannte Option zur Typerfassung: {0}.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Unbekannte Typerfassungsoption \"{0}\". Meinten Sie \"{1}\"?", "Unknown_watch_option_0_5078": "Unbekannte Überwachungsoption \"{0}\".", "Unknown_watch_option_0_Did_you_mean_1_5079": "Unbekannte Überwachungsoption \"{0}\". Meinten Sie \"{1}\"?", "Unreachable_code_detected_7027": "<PERSON><PERSON> wurde unerreichbarer Code erkannt.", "Unterminated_Unicode_escape_sequence_1199": "Nicht abgeschlossene Unicode-Escapesequenz.", "Unterminated_quoted_string_in_response_file_0_6045": "Nicht abgeschlossene Zeichenfolge in Anführungszeichen in der Datei \"{0}\".", "Unterminated_regular_expression_literal_1161": "Nicht abgeschlossenes reguläres Ausdrucksliteral.", "Unterminated_string_literal_1002": "Nicht abgeschlossenes Zeichenfolgenliteral.", "Unterminated_template_literal_1160": "Nicht abgeschlossenes Vorlagenliteral.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Nicht typisierte Funktionsaufrufe dürfen keine Typargumente annehmen.", "Unused_label_7028": "Nicht verwendete Bezeichnung.", "Unused_ts_expect_error_directive_2578": "<PERSON>cht verwendete @ts-expect-error-Direktive.", "Update_import_from_0_90058": "Import von \"{0}\" aktualisieren", "Updating_output_of_project_0_6373": "Ausgabe von Projekt \"{0}\" wird aktualisiert...", "Updating_output_timestamps_of_project_0_6359": "Ausgabezeitstempel von Projekt \"{0}\" werden aktualisiert...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Unveränderte Ausgabezeitstempel von Projekt \"{0}\" werden aktualisiert...", "Use_0_95174": "Verwenden Sie „{0}“.", "Use_Number_isNaN_in_all_conditions_95175": "Verwenden Sie „Number.isNaN“ unter allen Bedingungen.", "Use_element_access_for_0_95145": "Elementzugriff für \"{0}\" verwenden", "Use_element_access_for_all_undeclared_properties_95146": "Elementzugriff für alle nicht deklarierten Eigenschaften verwenden", "Use_synthetic_default_member_95016": "Verwenden Sie den synthetischen Member \"default\".", "Using_0_subpath_1_with_target_2_6404": "<PERSON><PERSON><PERSON><PERSON> von \"{0}\" Unterpfad \"{1}\" mit Ziel \"{2}\".", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "Das Verwenden einer Zeichenfolge in einer for...of-Anweisung wird nur in ECMAScript 5 oder höher unterstützt.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Bei Verwendung von --build wird tsc durch -b da<PERSON> veran<PERSON>t, sich eher wie ein Build-Orchestrator als ein Compiler zu verhalten. Damit wird der Aufbau von zusammengesetzten Projekten ausgelöst. Weitere Informationen dazu finden Sie unter {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Compileroptionen der Projektverweisumleitung \"{0}\" werden verwendet.", "VERSION_6036": "VERSION", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "Der Wert des Typs \"{0}\" verfügt über keine gemeinsamen Eigenschaften mit dem Typ \"{1}\". Wollten Sie ihn aufrufen?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "Der Wert des Typs \"{0}\" kann nicht aufgerufen werden. Wollten Sie \"new\" einschließen?", "Variable_0_implicitly_has_an_1_type_7005": "Die Variable \"{0}\" weist implizit einen Typ \"{1}\" auf.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "Die Variable \"{0}\" weist implizit einen Typ \"{1}\" auf, möglicherweise kann jedoch ein besserer Typ aus der Syntax abgeleitet werden.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "Die Variable \"{0}\" weist an einigen Stellen implizit den Typ \"{1}\" auf, möglicherweise kann jedoch ein besserer Typ aus der Syntax abgeleitet werden.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "Die Variable \"{0}\" weist an manchen Stellen implizit den Typ \"{1}\" auf, an denen der Typ nicht ermittelt werden kann.", "Variable_0_is_used_before_being_assigned_2454": "Die Variable \"{0}\" wird vor ihrer Zuweisung verwendet.", "Variable_declaration_expected_1134": "Eine Variablendeklaration wurde erwartet.", "Variable_declaration_list_cannot_be_empty_1123": "Die Variablendeklarationsliste darf nicht leer sein.", "Variable_declaration_not_allowed_at_this_location_1440": "Variablendeklaration ist an dieser Stelle nicht zulässig.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "Das variadic-Element an Position {0} in der Quelle stimmt nicht mit dem Element an Position {1} im Ziel überein.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Abweichungsanmerkungen werden nur in Typaliasnamen für Objekt-, Funktions-, Konstruktor- und zugeordnete Typen unterstützt.", "Version_0_6029": "Version {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Besuchen Sie https://aka.ms/tsconfig, um mehr über diese Datei zu erfahren.", "WATCH_OPTIONS_6918": "ÜBERWACHUNGSOPTIONEN", "Watch_and_Build_Modes_6250": "Überwachungs- und Buildmodi", "Watch_input_files_6005": "Eingabedateien überwachen.", "Watch_option_0_requires_a_value_of_type_1_5080": "Die Überwachungsoption \"{0}\" erfordert einen Wert vom Typ \"{1}\".", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "Wir können nur einen Typ für „{0}“ schreiben, indem hier ein Typ für den gesamten Parameter hinzugefügt wird.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Überprüfen Sie beim <PERSON> von Funktionen, ob Parameter und Rückgabewerte untertypkompatibel sind.", "When_type_checking_take_into_account_null_and_undefined_6699": "Berücksichtigen Sie bei der Typüberprüfung „null“ und „undefined“.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "<PERSON><PERSON><PERSON> an, ob eine veraltete Konsolenausgabe im Überwachungsmodus beibehalten wird, statt den Bildschirm zu löschen.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Alle ungültigen Zeichen mit einem Ausdruckscontainer umschließen", "Wrap_all_object_literal_with_parentheses_95116": "Gesamtes Objektliteral in Klammern einschließen", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Alle JSX ohne übergeordnetes Element mit JSX -Fragment umschließen", "Wrap_in_JSX_fragment_95120": "Mit JSX-Fragment umschließen", "Wrap_invalid_character_in_an_expression_container_95108": "Ungültiges Zeichen mit Ausdruckscontainer umschließen", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Schließen Sie den folgenden Text, der ein Objektliteral darstellt, in Klammern ein.", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Informationen zu allen Compileroptionen finden Sie unter {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Ein Modul kann nicht über einen globalen Import umbenannt werden.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "<PERSON>ement<PERSON>, die in einem Ordner \"node_modules\" definiert sind, können nicht umbenannt werden.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "<PERSON>ement<PERSON>, die in einem anderen Ordner \"node_modules\" definiert sind, können nicht umbenannt werden.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "<PERSON>e können keine Elemente umbenennen, die in der TypeScript-Standardbibliothek definiert sind.", "You_cannot_rename_this_element_8000": "<PERSON><PERSON> können dieses Element nicht umbenennen.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "\"{0}\" akze<PERSON><PERSON>t zu wenige Argumente, um hier als Decorator verwendet zu werden. Wollten Sie es zuerst aufrufen und \"@{0}()\" schreiben?", "_0_and_1_index_signatures_are_incompatible_2330": "Indexsignaturen \"{0}\" und \"{1}\" sind nicht kompatibel.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "Die Vorgänge \"{0}\" und \"{1}\" dürfen nicht ohne Klammern kombiniert werden.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "\"{0}\" ist zweimal angegeben. Das Attribut mit dem Namen \"{0}\" wird überschrieben.", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "\"{0}\" kann nur importiert werden, indem das Flag \"esModuleInterop\" aktiviert und ein Standardimport verwendet wird.", "_0_can_only_be_imported_by_using_a_default_import_2595": "\"{0}\" kann nur mithilfe eines Standardimports importiert werden.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "\"{0}\" kann nur mit einem Aufruf von \"require\" oder durch Aktivieren des Flags \"esModuleInterop\" und Verwendung eines Standardimports importiert werden.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "\"{0}\" kann nur mit einem Aufruf von \"require\" oder durch Verwendung eines Standardimports importiert werden.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "\"{0}\" kann nur mit \"import {1} = require({2})\" oder über einen Standardimport importiert werden.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "\"{0}\" kann nur mit \"import {1} = require({2})\" oder durch Aktivieren des Flags \"esModuleInterop\" und Verwendung eines Standardimports importiert werden.", "_0_cannot_be_compiled_under_isolatedModules_because_it_is_considered_a_global_script_file_Add_an_imp_1208": "\"{0}\" wird als globale Skriptdatei betrachtet und kann daher nicht unter \"--isolatedModules\" kompiliert werden. Fügen Sie zum Festlegen als Modul eine Import-, Export- oder eine leere \"export {}\"-Anweisung hinzu.", "_0_cannot_be_used_as_a_JSX_component_2786": "\"{0}\" kann nicht als JSX-Komponente verwendet werden.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "\"{0}\" kann nicht als Wert verwendet werden, weil der Export mit \"export type\" durchgeführt wurde.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "\"{0}\" kann nicht als Wert verwendet werden, weil der Import mit \"import type\" durchgeführt wurde.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "{0}-Komponenten akzeptieren Text nicht als untergeordnete Elemente. Der Text in der JSX weist den Typ \"string\" auf, aber für \"{1}\" wird der <PERSON>p \"{2}\" erwartet.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "\"{0}\" konnte mit einem arbiträren Typ instanziiert werden, der mit \"{1}\" möglicherweise in keinem Zusammenhang steht.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "{0}-Deklarationen können nur in TypeScript-Dateien verwendet werden.", "_0_expected_1005": "\"{0}\" wurde erwartet.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "\"{0}\" umfasst keinen exportierten Member namens \"{1}\". Meinten Sie \"{2}\"?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "\"{0}\" weist implizit einen Rückgabetyp \"{1}\" auf, möglicherweise kann jedoch ein besserer Typ aus der Syntax abgeleitet werden.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "\"{0}\" weist implizit den Typ \"any\" auf, weil keine Rückgabetypanmerkung vorhanden ist und darauf direkt oder indirekt in einem der Rückgabeausdrücke des Objekts verwiesen wird.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "\"{0}\" weist implizit den Typ \"any\" auf, weil keine Typanmerkung vorhanden ist und darauf direkt oder indirekt im eigenen Initialisierer verwiesen wird.", "_0_index_signatures_are_incompatible_2634": "\"{0}\" Indexsignaturen sind inkompatibel.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "\"{0}\" Indextyp \"{1}\" kann nicht \"{2}\" Indextyp \"{3}\" zugewiesen werden.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "\"{0}\" ist ein primitiver Typ, aber \"{1}\" ist ein Wrapperobjekt. Verwenden Sie vorzugsweise \"{0}\", wenn möglich.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "\"{0}\" ist ein <PERSON> und kann nicht in JavaScript-Dateien importiert werden. Verwenden Sie \"{1}\" in einer JSDoc-Typanmerkung.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_preserveValueImports_and_isolatedMod_1444": "„{0}“ ist ein Typ und muss mithilfe eines reinen Typimports importiert werden, wenn „preserveValueImports“ und isolatedModules“ beide aktiviert sind.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "„{0}“ ist eine nicht verwendete Umbenennung von „{1}“. Wollten Sie sie als Typanmerkung verwenden?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "\"{0}\" kann der Einschränkung vom Typ \"{1}\" zugewiesen werden, aber \"{1}\" könnte mit einem anderen Untertyp der Einschränkung \"{2}\" instanziiert werden.", "_0_is_automatically_exported_here_18044": "\"{0}\" wird hier automatisch exportiert.", "_0_is_declared_but_its_value_is_never_read_6133": "\"{0}\" ist deklariert, aber der zugehörige Wert wird nie gelesen.", "_0_is_declared_but_never_used_6196": "\"{0}\" ist de<PERSON><PERSON>t, wird aber nie verwendet.", "_0_is_declared_here_2728": "\"{0}\" wird hier de<PERSON><PERSON>.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "\"{0}\" ist als Eigenschaft in der Klasse \"{1}\" definiert, wird aber hier in \"{2}\" als Accessor überschrieben.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "\"{0}\" ist als Accessor in der Klasse \"{1}\" definiert, wird aber hier in \"{2}\" als Instanzeigenschaft überschrieben.", "_0_is_deprecated_6385": "\"{0}\" ist veraltet.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "\"{0}\" ist keine gültige Metaeigenschaft für das Schlüsselwort \"{1}\". Meinten Sie \"{2}\"?", "_0_is_not_allowed_as_a_parameter_name_1390": "\"{0}\" ist als Parametername nicht zulässig.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "\"{0}\" ist als Name für Variablendeklarationen nicht zulässig.", "_0_is_of_type_unknown_18046": "\"{0}\" ist vom Typ \"unbekannt\".", "_0_is_possibly_null_18047": "\"{0}\" ist möglicherweise \"null\".", "_0_is_possibly_null_or_undefined_18049": "\"{0}\" ist möglicherweise \"null\" oder \"nicht definiert\".", "_0_is_possibly_undefined_18048": "\"{0}\" ist möglicherweise nicht \"nicht definiert\".", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "Auf \"{0}\" wird direkt oder indirekt im eigenen Basisausdruck verwiesen.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "Auf \"{0}\" wird direkt oder indirekt in der eigenen Typanmerkung verwiesen.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "\"{0}\" wurde mehrmals angegeben, deshalb wird dieses Vorkommen überschrieben.", "_0_list_cannot_be_empty_1097": "Die {0}-<PERSON><PERSON> darf nicht leer sein.", "_0_modifier_already_seen_1030": "Der {0}-Modifizierer ist bereits vorhanden.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "Der Modifizierer „{0}“ kann nur für einen Typparameter einer Klasse, einer Schnittstelle oder eines Typalias verwendet werden.", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "Der Modifizierer \"{0}\" darf nicht für eine Konstruktordeklaration verwendet werden.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "Der Modifizierer \"{0}\" darf nicht für ein Modul- oder Namespaceelement verwendet werden.", "_0_modifier_cannot_appear_on_a_parameter_1090": "Der Modifizierer \"{0}\" darf nicht für einen Parameter verwendet werden.", "_0_modifier_cannot_appear_on_a_type_member_1070": "Der Modifizierer \"{0}\" darf nicht für einen Typmember verwendet werden.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "Der Modifizierer „{0}“ kann nicht für einen Typparameter verwendet werden", "_0_modifier_cannot_appear_on_an_index_signature_1071": "Der Modifizierer \"{0}\" darf nicht für eine Indexsignatur verwendet werden.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "Der Modifizierer \"{0}\" kann nicht für Klassenelemente dieser Art verwendet werden.", "_0_modifier_cannot_be_used_here_1042": "Der Modifizierer \"{0}\" kann hier nicht verwendet werden.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "Der Modifizierer \"{0}\" kann nicht in einem Umgebungskontext verwendet werden.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "Der Modifizierer \"{0}\" darf nicht mit dem Modifizierer \"{1}\" verwendet werden.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "Der Modifizierer \"{0}\" kann nicht mit einem privaten Bezeichner verwendet werden.", "_0_modifier_must_precede_1_modifier_1029": "Der Modifizierer \"{0}\" muss dem Modifizierer \"{1}\" vorangestellt sein.", "_0_needs_an_explicit_type_annotation_2782": "\"{0}\" erfordert eine explizite Typanmerkung.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "\"{0}\" bezieht sich nur auf einen <PERSON>, wird hier jedoch als Namespace verwendet.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "\"{0}\" bezieht sich nur auf einen <PERSON>, wird aber hier als Wert verwendet.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "\"{0}\" bezieht sich nur auf einen <PERSON>, wird hier jedoch als Wert verwendet. Wollten Sie \"{1} in {0}\" verwenden?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "\"{0}\" bezieht sich nur auf einen <PERSON>, wird hier jedoch als Wert verwendet. Müssen Sie Ihre Zielbibliothek ändern? Ändern Sie die Compileroption \"lib\" in \"es2015\" oder höher.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "\"{0}\" bezieht sich auf eine globale UMD, die aktuelle Datei ist jedoch ein Modul. <PERSON><PERSON><PERSON> in Betracht, stattdessen einen Import hinzuzufügen.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "\"{0}\" bezieht sich auf einen Wert, wird hier jedoch als Typ verwendet. Meinten Sie \"typeof {0}\"?", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_preserveVa_1446": "„{0}“ wird in eine reine Typdeklaration aufgelöst und muss mithilfe eines reinen Typimports importiert werden, wenn „preserveValueImports“ und isolatedModules“ beide aktiviert sind.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_isol_1448": "„{0}“ wird in eine reine Typdeklaration aufgelöst und muss mithilfe eines reinen Typreexports erneut exportiert werden, wenn „isolatedModules“ aktiviert ist.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "\"{0}\" sollte im CompilerOptions-Objekt der JSON-Konfigurationsdatei festgelegt werden.", "_0_tag_already_specified_1223": "Das Tag \"{0}\" wurde bereits angegeben.", "_0_was_also_declared_here_6203": "\"{0}\" wurde hier eben<PERSON> de<PERSON>.", "_0_was_exported_here_1377": "\"{0}\" wurde hier exportiert.", "_0_was_imported_here_1376": "\"{0}\" wurde hier import<PERSON>t.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "\"{0}\" ohne Rückgabetypanmerkung weist implizit einen Rückgabetyp \"{1}\" auf.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "\"{0}\" ohne Rückgabetypanmerkung weist implizit einen yield-Typ \"{1}\" auf.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "Der Modifizierer \"abstract\" darf nur für eine Klassen-, Methoden- oder Eigenschaftendeklaration verwendet werden.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "Der Accessormodifizierer kann nur in einer Eigenschaftendeklaration angezeigt werden.", "and_here_6204": "und hier.", "arguments_cannot_be_referenced_in_property_initializers_2815": "Auf \"arguments\" kann in Eigenschaftsinitialisierern nicht verwiesen werden.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "„auto“: Dateien mit imports, exports, import.meta, jsx (mit jsx: respond-jsx) oder esm-Format (mit module: node16+) als Module behandeln.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "await-Ausdrücke sind nur auf der obersten Ebene einer Datei zulässig, wenn diese Datei ein Modul ist. Diese Datei enthält jedoch keinerlei Importe oder Exporte. Erwägen Sie das Hinzufügen eines leeren \"export {}\", um diese Datei als Modul zu definieren.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "await-Ausdrücke sind nur innerhalb von asynchronen Funktionen und auf den obersten Modulebenen zulässig.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "await-Ausdrücke dürfen nicht in einem Parameterinitialisierer verwendet werden.", "await_has_no_effect_on_the_type_of_this_expression_80007": "\"await\" hat keine Auswirkungen auf den Typ dieses Ausdrucks.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "Die Option \"baseUrl\" ist auf \"{0}\" festgelegt. Dieser Wert wird verwendet, um den nicht relativen Modulnamen \"{1}\" auf<PERSON>lösen.", "can_only_be_used_at_the_start_of_a_file_18026": "\"#!\" kann nur am Anfang einer Datei verwendet werden.", "case_or_default_expected_1130": "\"case\" oder \"default\" wurde erwartet.", "catch_or_finally_expected_1472": "„catch“ oder „finally“ erwartet.", "const_declarations_can_only_be_declared_inside_a_block_1156": "const-Deklarationen können nur innerhalb eines Blocks deklariert werden.", "const_declarations_must_be_initialized_1155": "const-Deklarationen müssen initialisiert werden.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "Der const-Enumerationsmemberinitialisierer wurde in einen unendlichen Wert ausgewertet.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "Der const-Enumerationsmemberinitialisierer wurde in den unzulässigen Wert \"NaN\" ausgewertet.", "const_enum_member_initializers_can_only_contain_literal_values_and_other_computed_enum_values_2474": "Initialisierer von const-Enumerationsmembern dürfen nur Literalwerte und andere berechnete Enumerationswerte enthalten.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "const-Enumerationen können nur in Eigenschaften- bzw. Indexzugriffsausdrücken oder auf der rechten Seite einer Importdeklaration oder Exportzuweisung verwendet werden.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "\"constructor\" kann nicht als Parametereigenschaftsname verwendet werden.", "constructor_is_a_reserved_word_18012": "\"#constructor\" ist ein reserviertes Wort.", "default_Colon_6903": "Standard:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "\"delete\" kann für einen Bezeichner im Strict-Modus nicht aufgerufen werden.", "export_Asterisk_does_not_re_export_a_default_1195": "Mit \"export *\" wird ein Standardwert nicht erneut exportiert.", "export_can_only_be_used_in_TypeScript_files_8003": "\"export =\" kann nur in TypeScript-Dateien verwendet werden.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "Der Modifizierer \"export\" kann nicht auf Umgebungsmodule und Modulerweiterungen angewendet werden, da diese immer sichtbar sind.", "extends_clause_already_seen_1172": "Die extends-Klausel ist bereits vorhanden.", "extends_clause_must_precede_implements_clause_1173": "Die extends-<PERSON><PERSON> muss der implements-<PERSON>el vorangestellt sein.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "Die \"extends\"-<PERSON><PERSON> der exportierten Klasse \"{0}\" besitzt oder verwendet den privaten Namen \"{1}\".", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "Die extends-Klausel der exportierten Klasse besitzt oder verwendet den privaten Namen \"{0}\".", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "Die \"extends\"-<PERSON><PERSON> der exportierten Schnittstelle \"{0}\" besitzt oder verwendet den privaten Namen \"{1}\".", "false_unless_composite_is_set_6906": "'false', es sei denn 'composite' ist festgelegt", "false_unless_strict_is_set_6905": "'false', es sei denn, 'strict' ist festgelegt", "file_6025": "<PERSON><PERSON>", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "\"for await\"-Schleifen sind nur auf der obersten Ebene einer Datei zulässig, wenn diese Datei ein Modul ist. Diese Datei enthält jedoch keinerlei Importe oder Exporte. Erwägen Sie das Hinzufügen eines leeren \"export {}\", um diese Datei als Modul zu definieren.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "\"for await\"-Schleifen sind nur innerhalb von asynchronen Funktionen und auf den obersten Modulebenen zulässig.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "get- und set-Zugriffsmethoden können keine this-Parameter deklarieren.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "'[]', wenn 'files' angegeben ist, andernfalls '[\"**/*\"]5D;'", "implements_clause_already_seen_1175": "Die implements-Klausel ist bereits vorhanden.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "implements-<PERSON><PERSON>n können nur in TypeScript-Dateien verwendet werden.", "import_can_only_be_used_in_TypeScript_files_8002": "\"import... =\" kann nur in TypeScript-Dateien verwendet werden.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "infer-Deklarationen sind nur in der extends-Klausel eines bedingten Typs zulässig.", "let_declarations_can_only_be_declared_inside_a_block_1157": "let-Deklarationen können nur innerhalb eines Blocks deklariert werden.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "\"let\" darf nicht als Name in let- oder const-Deklarationen verwendet werden.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "Modul === 'AMD' oder 'UMD' oder 'System' oder 'ES6', dann 'Klassisch', and<PERSON><PERSON> 'Knoten'", "module_system_or_esModuleInterop_6904": "Modul === \"System\" oder esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "Der new-Ausdruck, in dessen Ziel eine Konstruktsignatur fehlt, weist implizit einen Typ \"any\" auf.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "'[\"node_modules\",\"bower_components\",\"jspm_packages\"]', plus der Wert von 'outDir', wenn ein Wert angegeben ist.", "one_of_Colon_6900": "<PERSON><PERSON> von:", "one_or_more_Colon_6901": "eins oder mehr:", "options_6024": "Optionen", "or_JSX_element_expected_1145": "'{' oder JSX-Element erwartet.", "or_expected_1144": "\"{\" oder \";\" wurde erwartet.", "package_json_does_not_have_a_0_field_6100": "\"package.json\" besitzt kein \"{0}\"-Feld.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "\"package.json\" weist keinen Eintrag \"typesVersions\" auf, der mit der Version {0} übereinstimmt.", "package_json_had_a_falsy_0_field_6220": "\"package.json\" enthielt ein \"falsy\" Feld \"{0}\".", "package_json_has_0_field_1_that_references_2_6101": "\"package.json\" weist das {0}-Feld \"{1}\" auf, das auf \"{2}\" verweist.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "\"package.json\" weist einen typesversion-Eintrag \"{0}\" auf, der kein gültiger semver-Bereich ist.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "\"package.json\" weist einen typesVersions-Eintrag \"{0}\" auf, der der Compilerversion \"{1}\" entspricht. Es wird nach einem Muster gesucht, das dem Modulnamen \"{2}\" entspricht.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "\"package.json\" weist ein Feld \"typesVersions\" mit versionsspezifischen Pfadzuordnungen auf.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "Der package.json-Bereich \"{0}\" ordnet den Bezeichner \"{1}\" explizit NULL zu.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "Der package.json-Bereich \"{0}\" weist einen ungültigen Typ für das Ziel des Spezifizierers \"{1}\" auf.", "package_json_scope_0_has_no_imports_defined_6273": "Für package.jsim, <PERSON><PERSON><PERSON> \"{0}\" wurden keine Importe definiert.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "Die Option \"paths\" wurde angegeben. Es wird nach einem Muster gesucht, das mit dem Modulnamen \"{0}\" übereinstimmt.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "Der Modifizierer \"readonly\" darf nur für eine Eigenschaftendeklaration oder Indexsignatur verwendet werden.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "Der Typmodifizierer \"readonly\" ist nur für Array- und Tupelliteraltypen zulässig.", "require_call_may_be_converted_to_an_import_80005": "Der Aufruf von \"require\" kann in einen Aufruf von \"import\" konvertiert werden.", "resolution_mode_assertions_are_only_supported_when_moduleResolution_is_node16_or_nodenext_1452": "„resolution-mode“-Zusicherungen werden nur unterstützt, wenn „moduleResolution“ „node16“ oder „nodenext“ ist.", "resolution_mode_assertions_are_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_wi_4125": "'resolution-mode' Behauptungen sind instabil. Verwenden Sie nächtliches TypeScript, um diesen Fehler zu unterdrücken. Versuchen Sie, mit „npm install -D typescript@next“ zu aktualisieren.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "„resolution-mode“ kann nur für reine Typenimporte festgelegt werden.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "„resolution-mode“ ist für Typimportassertionen der einzige gültige Schlüssel.", "resolution_mode_should_be_either_require_or_import_1453": "„resolution-mode“ muss entweder auf „require“ oder „import“ festgelegt sein.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "Die Option \"rootDirs\" wurde festgelegt. Sie wird zum Auflösen des relativen Modulnamens \"{0}\" verwendet.", "super_can_only_be_referenced_in_a_derived_class_2335": "Auf \"super\" kann nur in einer abgeleiteten Klasse verwiesen werden.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "Auf \"super\" kann nur in Membern abgeleiteter Klassen oder Objektliteralausdrücken verwiesen werden.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "Auf \"super\" kann nicht in einem berechneten Eigenschaftennamen verwiesen werden.", "super_cannot_be_referenced_in_constructor_arguments_2336": "Auf \"super\" kann nicht in Konstruktorargumenten verwiesen werden.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "\"super\" ist nur in Membern von Objektliteralausdrücken zulässig, wenn die Option \"target\" den Wert \"ES2015\" oder höher aufweist.", "super_may_not_use_type_arguments_2754": "\"super\" darf keine Typargumente verwenden.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "Vor dem Zugriff auf eine Eigenschaft von \"super\" im Konstruktor einer abgeleiteten Klasse muss \"super\" aufgerufen werden.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "\"super\" muss vor dem Zugreifen auf \"this\" im Konstruktor einer abgeleiteten Klasse aufgerufen werden.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "Auf \"super\" muss eine Argumentliste oder Memberzugriff folgen.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "Der Zugriff auf die super-Eigenschaft ist nur in einem Konstruktor, einer Memberfunktion oder einer Memberzugriffsmethode einer abgeleiteten Klasse zulässig.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "Auf \"this\" kann nicht in einem berechneten Eigenschaftennamen verwiesen werden.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "<PERSON>f \"this\" kann nicht in einem Modul- oder Namespacetext verwiesen werden.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "<PERSON>f \"this\" kann nicht in einem statischen Eigenschafteninitialisierer verwiesen werden.", "this_cannot_be_referenced_in_constructor_arguments_2333": "<PERSON>f \"this\" kann nicht in Konstruktorargumenten verwiesen werden.", "this_cannot_be_referenced_in_current_location_2332": "Auf \"this\" kann am aktuellen Speicherort nicht verwiesen werden.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "\"this\" weist implizit den Typ \"any\" auf, weil keine Typanmerkung vorhanden ist.", "true_for_ES2022_and_above_including_ESNext_6930": "\"true\" für ES2022 und höher, einschließlich ESNext.", "true_if_composite_false_otherwise_6909": "'true', wenn 'composite', and<PERSON><PERSON> 'false'", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: Der TypeScript-Compiler", "type_Colon_6902": "Typ:", "unique_symbol_types_are_not_allowed_here_1335": "\"unique symbol\"-Typen sind hier nicht zulässig.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "\"unique symbol\"-Typen sind nur für Variablen in einer Variablenanweisung zulässig.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "\"unique symbol\"-Typen dürfen für eine Variablendeklaration mit einem Bindungsnamen nicht verwendet werden.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "Eine Verwendung der Direktive \"use strict\" mit einer nicht einfachen Parameterliste ist nicht zugelassen.", "use_strict_directive_used_here_1349": "Die Direktive \"use strict\" wird hier verwendet.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "with-<PERSON><PERSON><PERSON><PERSON> sind in einem asynchronen Funktionsblock unzulässig.", "with_statements_are_not_allowed_in_strict_mode_1101": "this-Anweisungen sind im Strict-Modus unzulässig.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Der Ausdruck \"yield\" führt implizit zu einem Typ \"any\", weil der enthaltende Generator keine Anmerkung vom Rückgabetyp umfasst.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "yield-Ausdrücke dürfen nicht in einem Parameterinitialisierer verwendet werden."}