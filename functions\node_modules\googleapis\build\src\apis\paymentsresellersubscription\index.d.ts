/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { paymentsresellersubscription_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof paymentsresellersubscription_v1.Paymentsresellersubscription;
};
export declare function paymentsresellersubscription(version: 'v1'): paymentsresellersubscription_v1.Paymentsresellersubscription;
export declare function paymentsresellersubscription(options: paymentsresellersubscription_v1.Options): paymentsresellersubscription_v1.Paymentsresellersubscription;
declare const auth: AuthPlus;
export { auth };
export { paymentsresellersubscription_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
