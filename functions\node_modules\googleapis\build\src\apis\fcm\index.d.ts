/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { fcm_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof fcm_v1.Fcm;
};
export declare function fcm(version: 'v1'): fcm_v1.Fcm;
export declare function fcm(options: fcm_v1.Options): fcm_v1.Fcm;
declare const auth: AuthPlus;
export { auth };
export { fcm_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
