/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { gamesManagement_v1management } from './v1management';
export declare const VERSIONS: {
    v1management: typeof gamesManagement_v1management.Gamesmanagement;
};
export declare function gamesManagement(version: 'v1management'): gamesManagement_v1management.Gamesmanagement;
export declare function gamesManagement(options: gamesManagement_v1management.Options): gamesManagement_v1management.Gamesmanagement;
declare const auth: AuthPlus;
export { auth };
export { gamesManagement_v1management };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
