/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { pubsublite_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof pubsublite_v1.Pubsublite;
};
export declare function pubsublite(version: 'v1'): pubsublite_v1.Pubsublite;
export declare function pubsublite(options: pubsublite_v1.Options): pubsublite_v1.Pubsublite;
declare const auth: AuthPlus;
export { auth };
export { pubsublite_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
