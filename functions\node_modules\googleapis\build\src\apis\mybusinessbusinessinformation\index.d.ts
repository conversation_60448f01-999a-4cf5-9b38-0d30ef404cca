/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessbusinessinformation_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessbusinessinformation_v1.Mybusinessbusinessinformation;
};
export declare function mybusinessbusinessinformation(version: 'v1'): mybusinessbusinessinformation_v1.Mybusinessbusinessinformation;
export declare function mybusinessbusinessinformation(options: mybusinessbusinessinformation_v1.Options): mybusinessbusinessinformation_v1.Mybusinessbusinessinformation;
declare const auth: AuthPlus;
export { auth };
export { mybusinessbusinessinformation_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
