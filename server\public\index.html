<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="assets/sonar-logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SOÑAR - Advanced Email Monitoring Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
      :root {
        --primary: #c066e8;
        --primary-dark: #9a4bc0;
        --primary-light: #d084f0;
        --secondary: #6a3de8;
        --accent: #667eea;
        --text-dark: #1a1a1a;
        --text-medium: #4a5568;
        --text-light: #718096;
        --bg-primary: #ffffff;
        --bg-secondary: #f7fafc;
        --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
        --shadow-md: 0 10px 25px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
        --radius-sm: 8px;
        --radius-md: 12px;
        --radius-lg: 20px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: var(--bg-secondary);
        color: var(--text-dark);
        min-height: 100vh;
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        overflow-x: hidden;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      /* Navigation */
      .navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        z-index: 1000;
        transition: var(--transition);
      }

      .nav-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: 800;
        color: var(--text-dark);
        text-decoration: none;
      }

      .nav-logo img {
        width: 32px;
        height: 32px;
      }

      .nav-brand {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* Animated background */
      .bg-animation {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        overflow: hidden;
      }

      .floating-shape {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(192, 102, 232, 0.1) 0%, rgba(106, 61, 232, 0.05) 100%);
        animation: float-gentle 20s ease-in-out infinite;
      }

      @keyframes float-gentle {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
      }

      /* Hero Section */
      .hero {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        position: relative;
        background: var(--bg-gradient);
        color: white;
        overflow: hidden;
      }

      .hero::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
        z-index: 1;
      }

      .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }

      .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 2rem;
        animation: fadeInUp 0.8s ease-out;
      }

      .hero-title {
        font-size: clamp(2.5rem, 5vw, 4rem);
        font-weight: 800;
        line-height: 1.1;
        margin-bottom: 1.5rem;
        animation: fadeInUp 0.8s ease-out 0.2s both;
      }

      .hero-subtitle {
        font-size: 1.25rem;
        font-weight: 400;
        opacity: 0.9;
        margin-bottom: 2.5rem;
        line-height: 1.6;
        animation: fadeInUp 0.8s ease-out 0.4s both;
      }

      .hero-cta {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        animation: fadeInUp 0.8s ease-out 0.6s both;
      }

      .cta-primary, .cta-secondary {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 2rem;
        border-radius: var(--radius-md);
        font-weight: 600;
        text-decoration: none;
        transition: var(--transition);
        border: 2px solid transparent;
      }

      .cta-primary {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        backdrop-filter: blur(10px);
        border-color: rgba(255, 255, 255, 0.3);
      }

      .cta-primary:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
      }

      .cta-secondary {
        background: transparent;
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
      }

      .cta-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
      }

      /* Features Section */
      .features-section {
        padding: 6rem 0;
        background: var(--bg-primary);
      }

      .section-header {
        text-align: center;
        margin-bottom: 4rem;
      }

      .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 1rem;
      }

      .section-subtitle {
        font-size: 1.125rem;
        color: var(--text-medium);
        max-width: 600px;
        margin: 0 auto;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 4rem;
      }

      .feature-card {
        background: var(--bg-primary);
        border: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--radius-lg);
        padding: 2rem;
        text-align: center;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
      }

      .feature-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
      }

      .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-lg);
      }

      .feature-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }

      .feature-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 1rem;
      }

      .feature-description {
        color: var(--text-medium);
        line-height: 1.6;
      }

      /* Portals Section */
      .portals-section {
        padding: 6rem 0;
        background: var(--bg-secondary);
      }

      .portals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        max-width: 800px;
        margin: 0 auto;
      }

      .portal-card {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        transition: var(--transition);
        text-decoration: none;
        color: inherit;
        position: relative;
        border: 1px solid rgba(0, 0, 0, 0.05);
      }

      .portal-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-lg);
      }

      .portal-header {
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        padding: 2.5rem 2rem;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .portal-header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float-gentle 15s linear infinite;
      }

      .portal-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
      }

      .portal-icon svg {
        width: 40px;
        height: 40px;
      }

      .portal-body {
        padding: 2rem;
        text-align: center;
      }

      .portal-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 1rem;
      }

      .portal-description {
        color: var(--text-medium);
        margin-bottom: 2rem;
        line-height: 1.6;
      }

      .portal-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: var(--radius-md);
        font-weight: 600;
        text-decoration: none;
        transition: var(--transition);
        border: none;
        cursor: pointer;
      }

      .portal-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(192, 102, 232, 0.3);
      }

      /* Animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }

      /* Footer */
      .footer {
        background: var(--text-dark);
        color: white;
        padding: 3rem 0 2rem;
        text-align: center;
      }

      .footer-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      .footer-text {
        opacity: 0.8;
        margin-bottom: 1rem;
      }

      .footer-links {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
      }

      .footer-link {
        color: white;
        text-decoration: none;
        opacity: 0.8;
        transition: var(--transition);
      }

      .footer-link:hover {
        opacity: 1;
        color: var(--primary-light);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .container {
          padding: 0 1rem;
        }

        .nav-content {
          padding: 1rem;
        }

        .hero-content {
          padding: 1rem;
        }

        .hero-title {
          font-size: 2.5rem;
        }

        .hero-subtitle {
          font-size: 1.125rem;
        }

        .hero-cta {
          flex-direction: column;
          align-items: center;
        }

        .cta-primary, .cta-secondary {
          width: 100%;
          max-width: 280px;
          justify-content: center;
        }

        .features-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .portals-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .section-title {
          font-size: 2rem;
        }

        .footer-links {
          flex-direction: column;
          gap: 1rem;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: 2rem;
        }

        .feature-card, .portal-card {
          margin: 0 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-content">
        <a href="#" class="nav-logo">
          <img src="assets/sonar-logo.png" alt="SOÑAR Logo">
          <span class="nav-brand">SOÑAR</span>
        </a>
      </div>
    </nav>

    <!-- Animated Background -->
    <div class="bg-animation">
      <div class="floating-shape" style="width: 100px; height: 100px; top: 10%; left: 10%; animation-delay: 0s;"></div>
      <div class="floating-shape" style="width: 150px; height: 150px; top: 60%; right: 10%; animation-delay: 5s;"></div>
      <div class="floating-shape" style="width: 80px; height: 80px; bottom: 20%; left: 20%; animation-delay: 10s;"></div>
    </div>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <div class="hero-badge">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 12l2 2 4-4"/>
            <circle cx="12" cy="12" r="9"/>
          </svg>
          Advanced Email Monitoring Platform
        </div>

        <h1 class="hero-title">
          Welcome to <span style="background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">SOÑAR</span>
        </h1>

        <p class="hero-subtitle">
          Professional email monitoring and management platform with powerful analytics, comprehensive reporting, and seamless Gmail integration for businesses of all sizes.
        </p>

        <div class="hero-cta">
          <a href="#portals" class="cta-primary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M7 17l9.2-9.2M17 17V7H7"/>
            </svg>
            Get Started
          </a>
          <a href="#features" class="cta-secondary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polygon points="10,8 16,12 10,16 10,8"/>
            </svg>
            Learn More
          </a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Powerful Features</h2>
          <p class="section-subtitle">Everything you need to monitor, manage, and optimize your email communications</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
              </svg>
            </div>
            <h3 class="feature-title">Smart Email Management</h3>
            <p class="feature-description">Seamlessly manage your inbox and sent emails with our intuitive interface. Full Gmail integration with real-time synchronization and advanced filtering capabilities.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3v18h18"/>
                <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"/>
              </svg>
            </div>
            <h3 class="feature-title">Advanced Analytics</h3>
            <p class="feature-description">Comprehensive reporting and analytics dashboard with detailed insights into email patterns, delivery rates, and user engagement metrics.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="16" r="1"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
            </div>
            <h3 class="feature-title">Enterprise Security</h3>
            <p class="feature-description">Bank-level security with end-to-end encryption, comprehensive audit logs, and role-based access control for complete data protection.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Portals Section -->
    <section id="portals" class="portals-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Choose Your Portal</h2>
          <p class="section-subtitle">Access the right tools for your role and requirements</p>
        </div>

        <div class="portals-grid">
          <a href="https://sonar-client-2025.web.app" class="portal-card">
            <div class="portal-header">
              <div class="portal-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
              </div>
            </div>
            <div class="portal-body">
              <h3 class="portal-title">Client Portal</h3>
              <p class="portal-description">Access your personal email monitoring dashboard with analytics, inbox management, and communication history. Perfect for individual users and small teams.</p>
              <div class="portal-button">
                <span>Launch Client Portal</span>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M7 17l9.2-9.2M17 17V7H7"/>
                </svg>
              </div>
            </div>
          </a>

          <a href="https://sonar-admin-2025.web.app" class="portal-card">
            <div class="portal-header">
              <div class="portal-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                </svg>
              </div>
            </div>
            <div class="portal-body">
              <h3 class="portal-title">Admin Portal</h3>
              <p class="portal-description">Comprehensive administration panel with user management, system settings, audit logs, and advanced monitoring configurations for enterprise control.</p>
              <div class="portal-button">
                <span>Launch Admin Portal</span>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M7 17l9.2-9.2M17 17V7H7"/>
                </svg>
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-links">
          <a href="#" class="footer-link">Privacy Policy</a>
          <a href="#" class="footer-link">Terms of Service</a>
          <a href="#" class="footer-link">Support</a>
          <a href="#" class="footer-link">Documentation</a>
        </div>
        <p class="footer-text">&copy; 2025 SOÑAR Email Monitoring Platform. All rights reserved.</p>
      </div>
    </footer>

    <script>
      // Smooth scrolling for anchor links
      document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling to all links with hash
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
              target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }
          });
        });

        // Add scroll effect to navbar
        const navbar = document.querySelector('.navbar');
        let lastScrollTop = 0;

        window.addEventListener('scroll', function() {
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

          if (scrollTop > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
          } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
          }

          lastScrollTop = scrollTop;
        });

        // Add intersection observer for animations
        const observerOptions = {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.style.opacity = '1';
              entry.target.style.transform = 'translateY(0)';
            }
          });
        }, observerOptions);

        // Observe feature cards and portal cards
        document.querySelectorAll('.feature-card, .portal-card').forEach(card => {
          card.style.opacity = '0';
          card.style.transform = 'translateY(30px)';
          card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
          observer.observe(card);
        });
      });
    </script>
  </body>
</html>


















