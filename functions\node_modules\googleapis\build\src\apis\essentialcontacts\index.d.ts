/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { essentialcontacts_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof essentialcontacts_v1.Essentialcontacts;
};
export declare function essentialcontacts(version: 'v1'): essentialcontacts_v1.Essentialcontacts;
export declare function essentialcontacts(options: essentialcontacts_v1.Options): essentialcontacts_v1.Essentialcontacts;
declare const auth: AuthPlus;
export { auth };
export { essentialcontacts_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
