/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace biglake_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * BigLake API
     *
     * The BigLake API provides access to BigLake Metastore, a serverless, fully managed, and highly available metastore for open-source data that can be used for querying Apache Iceberg tables in BigQuery.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const biglake = google.biglake('v1');
     * ```
     */
    export class Biglake {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Catalog is the container of databases.
     */
    export interface Schema$Catalog {
        /**
         * Output only. The creation time of the catalog.
         */
        createTime?: string | null;
        /**
         * Output only. The deletion time of the catalog. Only set after the catalog is deleted.
         */
        deleteTime?: string | null;
        /**
         * Output only. The time when this catalog is considered expired. Only set after the catalog is deleted.
         */
        expireTime?: string | null;
        /**
         * Output only. The resource name. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}
         */
        name?: string | null;
        /**
         * Output only. The last modification time of the catalog.
         */
        updateTime?: string | null;
    }
    /**
     * Database is the container of tables.
     */
    export interface Schema$Database {
        /**
         * Output only. The creation time of the database.
         */
        createTime?: string | null;
        /**
         * Output only. The deletion time of the database. Only set after the database is deleted.
         */
        deleteTime?: string | null;
        /**
         * Output only. The time when this database is considered expired. Only set after the database is deleted.
         */
        expireTime?: string | null;
        /**
         * Options of a Hive database.
         */
        hiveOptions?: Schema$HiveDatabaseOptions;
        /**
         * Output only. The resource name. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}
         */
        name?: string | null;
        /**
         * The database type.
         */
        type?: string | null;
        /**
         * Output only. The last modification time of the database.
         */
        updateTime?: string | null;
    }
    /**
     * Options of a Hive database.
     */
    export interface Schema$HiveDatabaseOptions {
        /**
         * Cloud Storage folder URI where the database data is stored, starting with "gs://".
         */
        locationUri?: string | null;
        /**
         * Stores user supplied Hive database parameters.
         */
        parameters?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Options of a Hive table.
     */
    export interface Schema$HiveTableOptions {
        /**
         * Stores user supplied Hive table parameters.
         */
        parameters?: {
            [key: string]: string;
        } | null;
        /**
         * Stores physical storage information of the data.
         */
        storageDescriptor?: Schema$StorageDescriptor;
        /**
         * Hive table type. For example, MANAGED_TABLE, EXTERNAL_TABLE.
         */
        tableType?: string | null;
    }
    /**
     * Response message for the ListCatalogs method.
     */
    export interface Schema$ListCatalogsResponse {
        /**
         * The catalogs from the specified project.
         */
        catalogs?: Schema$Catalog[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for the ListDatabases method.
     */
    export interface Schema$ListDatabasesResponse {
        /**
         * The databases from the specified catalog.
         */
        databases?: Schema$Database[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for the ListTables method.
     */
    export interface Schema$ListTablesResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The tables from the specified database.
         */
        tables?: Schema$Table[];
    }
    /**
     * Request message for the RenameTable method in MetastoreService
     */
    export interface Schema$RenameTableRequest {
        /**
         * Required. The new `name` for the specified table, must be in the same database. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}/tables/{table_id\}
         */
        newName?: string | null;
    }
    /**
     * Serializer and deserializer information.
     */
    export interface Schema$SerDeInfo {
        /**
         * The fully qualified Java class name of the serialization library.
         */
        serializationLib?: string | null;
    }
    /**
     * Stores physical storage information of the data.
     */
    export interface Schema$StorageDescriptor {
        /**
         * The fully qualified Java class name of the input format.
         */
        inputFormat?: string | null;
        /**
         * Cloud Storage folder URI where the table data is stored, starting with "gs://".
         */
        locationUri?: string | null;
        /**
         * The fully qualified Java class name of the output format.
         */
        outputFormat?: string | null;
        /**
         * Serializer and deserializer information.
         */
        serdeInfo?: Schema$SerDeInfo;
    }
    /**
     * Represents a table.
     */
    export interface Schema$Table {
        /**
         * Output only. The creation time of the table.
         */
        createTime?: string | null;
        /**
         * Output only. The deletion time of the table. Only set after the table is deleted.
         */
        deleteTime?: string | null;
        /**
         * The checksum of a table object computed by the server based on the value of other fields. It may be sent on update requests to ensure the client has an up-to-date value before proceeding. It is only checked for update table operations.
         */
        etag?: string | null;
        /**
         * Output only. The time when this table is considered expired. Only set after the table is deleted.
         */
        expireTime?: string | null;
        /**
         * Options of a Hive table.
         */
        hiveOptions?: Schema$HiveTableOptions;
        /**
         * Output only. The resource name. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}/tables/{table_id\}
         */
        name?: string | null;
        /**
         * The table type.
         */
        type?: string | null;
        /**
         * Output only. The last modification time of the table.
         */
        updateTime?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        catalogs: Resource$Projects$Locations$Catalogs;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Catalogs {
        context: APIRequestContext;
        databases: Resource$Projects$Locations$Catalogs$Databases;
        constructor(context: APIRequestContext);
        /**
         * Creates a new catalog.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Catalogs$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Catalogs$Create, options?: MethodOptions): GaxiosPromise<Schema$Catalog>;
        create(params: Params$Resource$Projects$Locations$Catalogs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Catalogs$Create, options: MethodOptions | BodyResponseCallback<Schema$Catalog>, callback: BodyResponseCallback<Schema$Catalog>): void;
        create(params: Params$Resource$Projects$Locations$Catalogs$Create, callback: BodyResponseCallback<Schema$Catalog>): void;
        create(callback: BodyResponseCallback<Schema$Catalog>): void;
        /**
         * Deletes an existing catalog specified by the catalog ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Catalogs$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Catalogs$Delete, options?: MethodOptions): GaxiosPromise<Schema$Catalog>;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Delete, options: MethodOptions | BodyResponseCallback<Schema$Catalog>, callback: BodyResponseCallback<Schema$Catalog>): void;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Delete, callback: BodyResponseCallback<Schema$Catalog>): void;
        delete(callback: BodyResponseCallback<Schema$Catalog>): void;
        /**
         * Gets the catalog specified by the resource name.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Catalogs$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Catalogs$Get, options?: MethodOptions): GaxiosPromise<Schema$Catalog>;
        get(params: Params$Resource$Projects$Locations$Catalogs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Catalogs$Get, options: MethodOptions | BodyResponseCallback<Schema$Catalog>, callback: BodyResponseCallback<Schema$Catalog>): void;
        get(params: Params$Resource$Projects$Locations$Catalogs$Get, callback: BodyResponseCallback<Schema$Catalog>): void;
        get(callback: BodyResponseCallback<Schema$Catalog>): void;
        /**
         * List all catalogs in a specified project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Catalogs$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Catalogs$List, options?: MethodOptions): GaxiosPromise<Schema$ListCatalogsResponse>;
        list(params: Params$Resource$Projects$Locations$Catalogs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Catalogs$List, options: MethodOptions | BodyResponseCallback<Schema$ListCatalogsResponse>, callback: BodyResponseCallback<Schema$ListCatalogsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Catalogs$List, callback: BodyResponseCallback<Schema$ListCatalogsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListCatalogsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Create extends StandardParameters {
        /**
         * Required. The ID to use for the catalog, which will become the final component of the catalog's resource name.
         */
        catalogId?: string;
        /**
         * Required. The parent resource where this catalog will be created. Format: projects/{project_id_or_number\}/locations/{location_id\}
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Catalog;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Delete extends StandardParameters {
        /**
         * Required. The name of the catalog to delete. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Get extends StandardParameters {
        /**
         * Required. The name of the catalog to retrieve. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$List extends StandardParameters {
        /**
         * The maximum number of catalogs to return. The service may return fewer than this value. If unspecified, at most 50 catalogs will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListCatalogs` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCatalogs` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of catalogs. Format: projects/{project_id_or_number\}/locations/{location_id\}
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Catalogs$Databases {
        context: APIRequestContext;
        tables: Resource$Projects$Locations$Catalogs$Databases$Tables;
        constructor(context: APIRequestContext);
        /**
         * Creates a new database.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Create, options?: MethodOptions): GaxiosPromise<Schema$Database>;
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Create, options: MethodOptions | BodyResponseCallback<Schema$Database>, callback: BodyResponseCallback<Schema$Database>): void;
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Create, callback: BodyResponseCallback<Schema$Database>): void;
        create(callback: BodyResponseCallback<Schema$Database>): void;
        /**
         * Deletes an existing database specified by the database ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Delete, options?: MethodOptions): GaxiosPromise<Schema$Database>;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Delete, options: MethodOptions | BodyResponseCallback<Schema$Database>, callback: BodyResponseCallback<Schema$Database>): void;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Delete, callback: BodyResponseCallback<Schema$Database>): void;
        delete(callback: BodyResponseCallback<Schema$Database>): void;
        /**
         * Gets the database specified by the resource name.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Get, options?: MethodOptions): GaxiosPromise<Schema$Database>;
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Get, options: MethodOptions | BodyResponseCallback<Schema$Database>, callback: BodyResponseCallback<Schema$Database>): void;
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Get, callback: BodyResponseCallback<Schema$Database>): void;
        get(callback: BodyResponseCallback<Schema$Database>): void;
        /**
         * List all databases in a specified catalog.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Catalogs$Databases$List, options?: MethodOptions): GaxiosPromise<Schema$ListDatabasesResponse>;
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$List, options: MethodOptions | BodyResponseCallback<Schema$ListDatabasesResponse>, callback: BodyResponseCallback<Schema$ListDatabasesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$List, callback: BodyResponseCallback<Schema$ListDatabasesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDatabasesResponse>): void;
        /**
         * Updates an existing database specified by the database ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Patch, options?: MethodOptions): GaxiosPromise<Schema$Database>;
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Patch, options: MethodOptions | BodyResponseCallback<Schema$Database>, callback: BodyResponseCallback<Schema$Database>): void;
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Patch, callback: BodyResponseCallback<Schema$Database>): void;
        patch(callback: BodyResponseCallback<Schema$Database>): void;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Create extends StandardParameters {
        /**
         * Required. The ID to use for the database, which will become the final component of the database's resource name.
         */
        databaseId?: string;
        /**
         * Required. The parent resource where this database will be created. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Database;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Delete extends StandardParameters {
        /**
         * Required. The name of the database to delete. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Get extends StandardParameters {
        /**
         * Required. The name of the database to retrieve. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$List extends StandardParameters {
        /**
         * The maximum number of databases to return. The service may return fewer than this value. If unspecified, at most 50 databases will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListDatabases` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDatabases` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of databases. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Patch extends StandardParameters {
        /**
         * Output only. The resource name. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}
         */
        name?: string;
        /**
         * The list of fields to update. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask If not set, defaults to all of the fields that are allowed to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Database;
    }
    export class Resource$Projects$Locations$Catalogs$Databases$Tables {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new table.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Create, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Create, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        create(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Create, callback: BodyResponseCallback<Schema$Table>): void;
        create(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * Deletes an existing table specified by the table ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Delete, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Delete, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        delete(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Delete, callback: BodyResponseCallback<Schema$Table>): void;
        delete(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * Gets the table specified by the resource name.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Get, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Get, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        get(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Get, callback: BodyResponseCallback<Schema$Table>): void;
        get(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * List all tables in a specified database.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$List, options?: MethodOptions): GaxiosPromise<Schema$ListTablesResponse>;
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$List, options: MethodOptions | BodyResponseCallback<Schema$ListTablesResponse>, callback: BodyResponseCallback<Schema$ListTablesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$List, callback: BodyResponseCallback<Schema$ListTablesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTablesResponse>): void;
        /**
         * Updates an existing table specified by the table ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Patch, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Patch, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        patch(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Patch, callback: BodyResponseCallback<Schema$Table>): void;
        patch(callback: BodyResponseCallback<Schema$Table>): void;
        /**
         * Renames an existing table specified by the table ID.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        rename(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Rename, options: StreamMethodOptions): GaxiosPromise<Readable>;
        rename(params?: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Rename, options?: MethodOptions): GaxiosPromise<Schema$Table>;
        rename(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Rename, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        rename(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Rename, options: MethodOptions | BodyResponseCallback<Schema$Table>, callback: BodyResponseCallback<Schema$Table>): void;
        rename(params: Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Rename, callback: BodyResponseCallback<Schema$Table>): void;
        rename(callback: BodyResponseCallback<Schema$Table>): void;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Create extends StandardParameters {
        /**
         * Required. The parent resource where this table will be created. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}
         */
        parent?: string;
        /**
         * Required. The ID to use for the table, which will become the final component of the table's resource name.
         */
        tableId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Table;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Delete extends StandardParameters {
        /**
         * Required. The name of the table to delete. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}/tables/{table_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Get extends StandardParameters {
        /**
         * Required. The name of the table to retrieve. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}/tables/{table_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Tables$List extends StandardParameters {
        /**
         * The maximum number of tables to return. The service may return fewer than this value. If unspecified, at most 50 tables will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListTables` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListTables` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of tables. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}
         */
        parent?: string;
        /**
         * The view for the returned tables.
         */
        view?: string;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Patch extends StandardParameters {
        /**
         * Output only. The resource name. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}/tables/{table_id\}
         */
        name?: string;
        /**
         * The list of fields to update. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask If not set, defaults to all of the fields that are allowed to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Table;
    }
    export interface Params$Resource$Projects$Locations$Catalogs$Databases$Tables$Rename extends StandardParameters {
        /**
         * Required. The table's `name` field is used to identify the table to rename. Format: projects/{project_id_or_number\}/locations/{location_id\}/catalogs/{catalog_id\}/databases/{database_id\}/tables/{table_id\}
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RenameTableRequest;
    }
    export {};
}
