/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { vectortile_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof vectortile_v1.Vectortile;
};
export declare function vectortile(version: 'v1'): vectortile_v1.Vectortile;
export declare function vectortile(options: vectortile_v1.Options): vectortile_v1.Vectortile;
declare const auth: AuthPlus;
export { auth };
export { vectortile_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
