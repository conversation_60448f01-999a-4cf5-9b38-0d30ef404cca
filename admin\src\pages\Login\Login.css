/* Modern Auth Container */
.modern-auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
}

/* Modern Auth Card */
.modern-auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  width: 100%;
  max-width: 440px;
  overflow: hidden;
  position: relative;
  z-index: 10;
  animation: slideUp 0.6s ease-out;
}

/* Auth Header */
.auth-header {
  padding: 40px 32px 20px;
  text-align: center;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  margin-bottom: 8px;
  animation: float 3s ease-in-out infinite;
}

.auth-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
}

.brand-gradient {
  background: linear-gradient(135deg, #cc65e8 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.auth-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

/* Modern Form */
.modern-auth-form {
  padding: 0 32px 40px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.modern-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modern-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.modern-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  z-index: 2;
  pointer-events: none;
}

.modern-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background-color: #ffffff;
  font-size: 16px;
  font-weight: 400;
  color: #1f2937;
  transition: all 0.3s ease;
  outline: none;
}

.modern-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.modern-input:focus {
  border-color: #cc65e8;
  box-shadow: 0 0 0 3px rgba(204, 101, 232, 0.1);
  transform: translateY(-1px);
}

/* Modern Password Toggle */
.modern-password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9ca3af;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modern-password-toggle:hover {
  color: #cc65e8;
  background-color: rgba(204, 101, 232, 0.1);
}

/* Modern Form Options */
.modern-form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Modern Checkbox */
.modern-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  gap: 12px;
}

.modern-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  height: 20px;
  width: 20px;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  position: relative;
  transition: all 0.3s ease;
}

.modern-checkbox:hover .checkmark {
  border-color: #cc65e8;
}

.modern-checkbox input:checked ~ .checkmark {
  background-color: #cc65e8;
  border-color: #cc65e8;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.modern-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

/* Forgot Password Link */
.forgot-link {
  font-size: 14px;
  font-weight: 500;
  color: #cc65e8;
  text-decoration: none;
  transition: all 0.2s ease;
}

.forgot-link:hover {
  color: #a855f7;
  text-decoration: underline;
}

/* Modern Auth Button */
.modern-auth-button {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #cc65e8 0%, #a855f7 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 56px;
  position: relative;
  overflow: hidden;
}

.modern-auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-auth-button:hover::before {
  left: 100%;
}

.modern-auth-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(204, 101, 232, 0.3);
}

.modern-auth-button:active {
  transform: translateY(0);
}

.modern-auth-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.modern-auth-button:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Button Loading State */
.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Error Message */
.modern-error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #fef2f2;
  color: #dc2626;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #fecaca;
  font-size: 14px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

.modern-error-message svg {
  flex-shrink: 0;
}

/* Background Decoration */
.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-auth-container {
    padding: 8px;
    min-height: 100vh;
    align-items: center;
    justify-content: center;
  }

  .modern-auth-card {
    max-width: 95%;
    width: 95%;
    border-radius: 16px;
    margin: 0;
    min-height: auto;
    max-height: 90vh;
    overflow-y: auto;
  }

  .auth-header {
    padding: 24px 20px 12px;
  }

  .modern-auth-form {
    padding: 0 20px 24px;
  }

  .auth-title {
    font-size: 22px;
  }

  .modern-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

@media (max-width: 480px) {
  .modern-auth-container {
    padding: 4px;
  }

  .modern-auth-card {
    max-width: 98%;
    width: 98%;
    border-radius: 12px;
    margin: 0;
  }

  .auth-header {
    padding: 20px 16px 8px;
  }

  .modern-auth-form {
    padding: 0 16px 20px;
  }

  .auth-title {
    font-size: 20px;
  }

  .auth-subtitle {
    font-size: 14px;
  }
}

/* Ensure full viewport height on mobile */
@media (max-height: 600px) {
  .modern-auth-container {
    align-items: center;
    padding: 4px;
    min-height: 100vh;
  }

  .modern-auth-card {
    max-height: 95vh;
    overflow-y: auto;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .modern-auth-card {
    max-width: 100%;
    width: 100%;
    border-radius: 8px;
  }

  .auth-header {
    padding: 16px 12px 8px;
  }

  .modern-auth-form {
    padding: 0 12px 16px;
  }

  .auth-title {
    font-size: 18px;
  }
}

