/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { iamcredentials_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof iamcredentials_v1.Iamcredentials;
};
export declare function iamcredentials(version: 'v1'): iamcredentials_v1.Iamcredentials;
export declare function iamcredentials(options: iamcredentials_v1.Options): iamcredentials_v1.Iamcredentials;
declare const auth: AuthPlus;
export { auth };
export { iamcredentials_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
