/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace cloudshell_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Shell API
     *
     * Allows users to start, configure, and connect to interactive shell sessions running in the cloud.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const cloudshell = google.cloudshell('v1');
     * ```
     */
    export class Cloudshell {
        context: APIRequestContext;
        operations: Resource$Operations;
        users: Resource$Users;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Message included in the metadata field of operations returned from AddPublicKey.
     */
    export interface Schema$AddPublicKeyMetadata {
    }
    /**
     * Request message for AddPublicKey.
     */
    export interface Schema$AddPublicKeyRequest {
        /**
         * Key that should be added to the environment. Supported formats are `ssh-dss` (see RFC4253), `ssh-rsa` (see RFC4253), `ecdsa-sha2-nistp256` (see RFC5656), `ecdsa-sha2-nistp384` (see RFC5656) and `ecdsa-sha2-nistp521` (see RFC5656). It should be structured as <format\> <content\>, where <content\> part is encoded with Base64.
         */
        key?: string | null;
    }
    /**
     * Response message for AddPublicKey.
     */
    export interface Schema$AddPublicKeyResponse {
        /**
         * Key that was added to the environment.
         */
        key?: string | null;
    }
    /**
     * Message included in the metadata field of operations returned from AuthorizeEnvironment.
     */
    export interface Schema$AuthorizeEnvironmentMetadata {
    }
    /**
     * Request message for AuthorizeEnvironment.
     */
    export interface Schema$AuthorizeEnvironmentRequest {
        /**
         * The OAuth access token that should be sent to the environment.
         */
        accessToken?: string | null;
        /**
         * The time when the credentials expire. If not set, defaults to one hour from when the server received the request.
         */
        expireTime?: string | null;
        /**
         * The OAuth ID token that should be sent to the environment.
         */
        idToken?: string | null;
    }
    /**
     * Response message for AuthorizeEnvironment.
     */
    export interface Schema$AuthorizeEnvironmentResponse {
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Message included in the metadata field of operations returned from CreateEnvironment.
     */
    export interface Schema$CreateEnvironmentMetadata {
    }
    /**
     * Message included in the metadata field of operations returned from DeleteEnvironment.
     */
    export interface Schema$DeleteEnvironmentMetadata {
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * A Cloud Shell environment, which is defined as the combination of a Docker image specifying what is installed on the environment and a home directory containing the user's data that will remain across sessions. Each user has at least an environment with the ID "default".
     */
    export interface Schema$Environment {
        /**
         * Required. Immutable. Full path to the Docker image used to run this environment, e.g. "gcr.io/dev-con/cloud-devshell:latest".
         */
        dockerImage?: string | null;
        /**
         * Output only. The environment's identifier, unique among the user's environments.
         */
        id?: string | null;
        /**
         * Immutable. Full name of this resource, in the format `users/{owner_email\}/environments/{environment_id\}`. `{owner_email\}` is the email address of the user to whom this environment belongs, and `{environment_id\}` is the identifier of this environment. For example, `users/<EMAIL>/environments/default`.
         */
        name?: string | null;
        /**
         * Output only. Public keys associated with the environment. Clients can connect to this environment via SSH only if they possess a private key corresponding to at least one of these public keys. Keys can be added to or removed from the environment using the AddPublicKey and RemovePublicKey methods.
         */
        publicKeys?: string[] | null;
        /**
         * Output only. Host to which clients can connect to initiate SSH sessions with the environment.
         */
        sshHost?: string | null;
        /**
         * Output only. Port to which clients can connect to initiate SSH sessions with the environment.
         */
        sshPort?: number | null;
        /**
         * Output only. Username that clients should use when initiating SSH sessions with the environment.
         */
        sshUsername?: string | null;
        /**
         * Output only. Current execution state of this environment.
         */
        state?: string | null;
        /**
         * Output only. Host to which clients can connect to initiate HTTPS or WSS connections with the environment.
         */
        webHost?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Message included in the metadata field of operations returned from RemovePublicKey.
     */
    export interface Schema$RemovePublicKeyMetadata {
    }
    /**
     * Request message for RemovePublicKey.
     */
    export interface Schema$RemovePublicKeyRequest {
        /**
         * Key that should be removed from the environment.
         */
        key?: string | null;
    }
    /**
     * Response message for RemovePublicKey.
     */
    export interface Schema$RemovePublicKeyResponse {
    }
    /**
     * Message included in the metadata field of operations returned from StartEnvironment.
     */
    export interface Schema$StartEnvironmentMetadata {
        /**
         * Current state of the environment being started.
         */
        state?: string | null;
    }
    /**
     * Request message for StartEnvironment.
     */
    export interface Schema$StartEnvironmentRequest {
        /**
         * The initial access token passed to the environment. If this is present and valid, the environment will be pre-authenticated with gcloud so that the user can run gcloud commands in Cloud Shell without having to log in. This code can be updated later by calling AuthorizeEnvironment.
         */
        accessToken?: string | null;
        /**
         * Public keys that should be added to the environment before it is started.
         */
        publicKeys?: string[] | null;
    }
    /**
     * Message included in the response field of operations returned from StartEnvironment once the operation is complete.
     */
    export interface Schema$StartEnvironmentResponse {
        /**
         * Environment that was started.
         */
        environment?: Schema$Environment;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Users {
        context: APIRequestContext;
        environments: Resource$Users$Environments;
        constructor(context: APIRequestContext);
    }
    export class Resource$Users$Environments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Adds a public SSH key to an environment, allowing clients with the corresponding private key to connect to that environment via SSH. If a key with the same content already exists, this will error with ALREADY_EXISTS.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        addPublicKey(params: Params$Resource$Users$Environments$Addpublickey, options: StreamMethodOptions): GaxiosPromise<Readable>;
        addPublicKey(params?: Params$Resource$Users$Environments$Addpublickey, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        addPublicKey(params: Params$Resource$Users$Environments$Addpublickey, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        addPublicKey(params: Params$Resource$Users$Environments$Addpublickey, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        addPublicKey(params: Params$Resource$Users$Environments$Addpublickey, callback: BodyResponseCallback<Schema$Operation>): void;
        addPublicKey(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sends OAuth credentials to a running environment on behalf of a user. When this completes, the environment will be authorized to run various Google Cloud command line tools without requiring the user to manually authenticate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        authorize(params: Params$Resource$Users$Environments$Authorize, options: StreamMethodOptions): GaxiosPromise<Readable>;
        authorize(params?: Params$Resource$Users$Environments$Authorize, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        authorize(params: Params$Resource$Users$Environments$Authorize, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        authorize(params: Params$Resource$Users$Environments$Authorize, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        authorize(params: Params$Resource$Users$Environments$Authorize, callback: BodyResponseCallback<Schema$Operation>): void;
        authorize(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets an environment. Returns NOT_FOUND if the environment does not exist.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Users$Environments$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Users$Environments$Get, options?: MethodOptions): GaxiosPromise<Schema$Environment>;
        get(params: Params$Resource$Users$Environments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Users$Environments$Get, options: MethodOptions | BodyResponseCallback<Schema$Environment>, callback: BodyResponseCallback<Schema$Environment>): void;
        get(params: Params$Resource$Users$Environments$Get, callback: BodyResponseCallback<Schema$Environment>): void;
        get(callback: BodyResponseCallback<Schema$Environment>): void;
        /**
         * Removes a public SSH key from an environment. Clients will no longer be able to connect to the environment using the corresponding private key. If a key with the same content is not present, this will error with NOT_FOUND.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        removePublicKey(params: Params$Resource$Users$Environments$Removepublickey, options: StreamMethodOptions): GaxiosPromise<Readable>;
        removePublicKey(params?: Params$Resource$Users$Environments$Removepublickey, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        removePublicKey(params: Params$Resource$Users$Environments$Removepublickey, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        removePublicKey(params: Params$Resource$Users$Environments$Removepublickey, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        removePublicKey(params: Params$Resource$Users$Environments$Removepublickey, callback: BodyResponseCallback<Schema$Operation>): void;
        removePublicKey(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Starts an existing environment, allowing clients to connect to it. The returned operation will contain an instance of StartEnvironmentMetadata in its metadata field. Users can wait for the environment to start by polling this operation via GetOperation. Once the environment has finished starting and is ready to accept connections, the operation will contain a StartEnvironmentResponse in its response field.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        start(params: Params$Resource$Users$Environments$Start, options: StreamMethodOptions): GaxiosPromise<Readable>;
        start(params?: Params$Resource$Users$Environments$Start, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        start(params: Params$Resource$Users$Environments$Start, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        start(params: Params$Resource$Users$Environments$Start, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        start(params: Params$Resource$Users$Environments$Start, callback: BodyResponseCallback<Schema$Operation>): void;
        start(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Users$Environments$Addpublickey extends StandardParameters {
        /**
         * Environment this key should be added to, e.g. `users/me/environments/default`.
         */
        environment?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddPublicKeyRequest;
    }
    export interface Params$Resource$Users$Environments$Authorize extends StandardParameters {
        /**
         * Name of the resource that should receive the credentials, for example `users/me/environments/default` or `users/<EMAIL>/environments/default`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizeEnvironmentRequest;
    }
    export interface Params$Resource$Users$Environments$Get extends StandardParameters {
        /**
         * Required. Name of the requested resource, for example `users/me/environments/default` or `users/<EMAIL>/environments/default`.
         */
        name?: string;
    }
    export interface Params$Resource$Users$Environments$Removepublickey extends StandardParameters {
        /**
         * Environment this key should be removed from, e.g. `users/me/environments/default`.
         */
        environment?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RemovePublicKeyRequest;
    }
    export interface Params$Resource$Users$Environments$Start extends StandardParameters {
        /**
         * Name of the resource that should be started, for example `users/me/environments/default` or `users/<EMAIL>/environments/default`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StartEnvironmentRequest;
    }
    export {};
}
