/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { merchantapi_accounts_v1beta } from './accounts_v1beta';
import { merchantapi_conversions_v1beta } from './conversions_v1beta';
import { merchantapi_datasources_v1beta } from './datasources_v1beta';
import { merchantapi_inventories_v1beta } from './inventories_v1beta';
import { merchantapi_lfp_v1beta } from './lfp_v1beta';
import { merchantapi_notifications_v1beta } from './notifications_v1beta';
import { merchantapi_products_v1beta } from './products_v1beta';
import { merchantapi_promotions_v1beta } from './promotions_v1beta';
import { merchantapi_quota_v1beta } from './quota_v1beta';
import { merchantapi_reports_v1beta } from './reports_v1beta';
import { merchantapi_reviews_v1beta } from './reviews_v1beta';
export declare const VERSIONS: {
    accounts_v1beta: typeof merchantapi_accounts_v1beta.Merchantapi;
    conversions_v1beta: typeof merchantapi_conversions_v1beta.Merchantapi;
    datasources_v1beta: typeof merchantapi_datasources_v1beta.Merchantapi;
    inventories_v1beta: typeof merchantapi_inventories_v1beta.Merchantapi;
    lfp_v1beta: typeof merchantapi_lfp_v1beta.Merchantapi;
    notifications_v1beta: typeof merchantapi_notifications_v1beta.Merchantapi;
    products_v1beta: typeof merchantapi_products_v1beta.Merchantapi;
    promotions_v1beta: typeof merchantapi_promotions_v1beta.Merchantapi;
    quota_v1beta: typeof merchantapi_quota_v1beta.Merchantapi;
    reports_v1beta: typeof merchantapi_reports_v1beta.Merchantapi;
    reviews_v1beta: typeof merchantapi_reviews_v1beta.Merchantapi;
};
export declare function merchantapi(version: 'accounts_v1beta'): merchantapi_accounts_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_accounts_v1beta.Options): merchantapi_accounts_v1beta.Merchantapi;
export declare function merchantapi(version: 'conversions_v1beta'): merchantapi_conversions_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_conversions_v1beta.Options): merchantapi_conversions_v1beta.Merchantapi;
export declare function merchantapi(version: 'datasources_v1beta'): merchantapi_datasources_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_datasources_v1beta.Options): merchantapi_datasources_v1beta.Merchantapi;
export declare function merchantapi(version: 'inventories_v1beta'): merchantapi_inventories_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_inventories_v1beta.Options): merchantapi_inventories_v1beta.Merchantapi;
export declare function merchantapi(version: 'lfp_v1beta'): merchantapi_lfp_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_lfp_v1beta.Options): merchantapi_lfp_v1beta.Merchantapi;
export declare function merchantapi(version: 'notifications_v1beta'): merchantapi_notifications_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_notifications_v1beta.Options): merchantapi_notifications_v1beta.Merchantapi;
export declare function merchantapi(version: 'products_v1beta'): merchantapi_products_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_products_v1beta.Options): merchantapi_products_v1beta.Merchantapi;
export declare function merchantapi(version: 'promotions_v1beta'): merchantapi_promotions_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_promotions_v1beta.Options): merchantapi_promotions_v1beta.Merchantapi;
export declare function merchantapi(version: 'quota_v1beta'): merchantapi_quota_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_quota_v1beta.Options): merchantapi_quota_v1beta.Merchantapi;
export declare function merchantapi(version: 'reports_v1beta'): merchantapi_reports_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_reports_v1beta.Options): merchantapi_reports_v1beta.Merchantapi;
export declare function merchantapi(version: 'reviews_v1beta'): merchantapi_reviews_v1beta.Merchantapi;
export declare function merchantapi(options: merchantapi_reviews_v1beta.Options): merchantapi_reviews_v1beta.Merchantapi;
declare const auth: AuthPlus;
export { auth };
export { merchantapi_accounts_v1beta };
export { merchantapi_conversions_v1beta };
export { merchantapi_datasources_v1beta };
export { merchantapi_inventories_v1beta };
export { merchantapi_lfp_v1beta };
export { merchantapi_notifications_v1beta };
export { merchantapi_products_v1beta };
export { merchantapi_promotions_v1beta };
export { merchantapi_quota_v1beta };
export { merchantapi_reports_v1beta };
export { merchantapi_reviews_v1beta };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
