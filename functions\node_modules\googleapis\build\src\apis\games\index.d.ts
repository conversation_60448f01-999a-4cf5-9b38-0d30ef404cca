/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { games_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof games_v1.Games;
};
export declare function games(version: 'v1'): games_v1.Games;
export declare function games(options: games_v1.Options): games_v1.Games;
declare const auth: AuthPlus;
export { auth };
export { games_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
