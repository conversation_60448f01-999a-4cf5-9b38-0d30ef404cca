/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { sql_v1beta4 } from './v1beta4';
export declare const VERSIONS: {
    v1beta4: typeof sql_v1beta4.Sql;
};
export declare function sql(version: 'v1beta4'): sql_v1beta4.Sql;
export declare function sql(options: sql_v1beta4.Options): sql_v1beta4.Sql;
declare const auth: AuthPlus;
export { auth };
export { sql_v1beta4 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
