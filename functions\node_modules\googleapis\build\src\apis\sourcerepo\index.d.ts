/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { sourcerepo_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof sourcerepo_v1.Sourcerepo;
};
export declare function sourcerepo(version: 'v1'): sourcerepo_v1.Sourcerepo;
export declare function sourcerepo(options: sourcerepo_v1.Options): sourcerepo_v1.Sourcerepo;
declare const auth: AuthPlus;
export { auth };
export { sourcerepo_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
