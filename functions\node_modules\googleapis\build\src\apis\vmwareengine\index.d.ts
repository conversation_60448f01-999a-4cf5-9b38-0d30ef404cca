/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { vmwareengine_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof vmwareengine_v1.Vmwareengine;
};
export declare function vmwareengine(version: 'v1'): vmwareengine_v1.Vmwareengine;
export declare function vmwareengine(options: vmwareengine_v1.Options): vmwareengine_v1.Vmwareengine;
declare const auth: AuthPlus;
export { auth };
export { vmwareengine_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
