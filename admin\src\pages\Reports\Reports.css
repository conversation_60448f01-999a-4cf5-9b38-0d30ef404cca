.reports-container {
  padding: 20px 40px; /* Increased side padding to match dashboard */
  max-width: 1400px; /* Increased max-width to match dashboard */
  margin: 0 auto;
}

.reports-container h1 {
  margin-bottom: 20px;
  color: #333;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  gap: 10px;
  align-items: center;
}

.refresh-icon-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  color: #4a6cf7;
}

.refresh-icon-btn:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.refresh-icon {
  animation: spin 1s linear infinite paused;
}

.refresh-icon-btn:hover .refresh-icon {
  animation-play-state: running;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
}

.filter-button:hover {
  background-color: #e9ecef;
}

.export-dropdown {
  position: relative;
}

.export-dropdown button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
}

.export-options {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
}

.export-option {
  padding: 8px 16px;
  cursor: pointer;
}

.export-option:hover {
  background-color: #f8f9fa;
}

.export-option.selected {
  background-color: #e9ecef;
  font-weight: bold;
}

.reports-filters-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.filter-group {
  flex: 1;
}

.filter-group label {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  font-weight: 500;
}

.filter-group input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-inputs input {
  flex: 1;
  padding: 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.apply-filters {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

.reset-filters {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

.active-filters-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.active-filters-label {
  font-weight: 500;
  margin-right: 8px;
}

.active-filter-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #e9ecef;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 14px;
}

.remove-filter {
  cursor: pointer;
}

.clear-all-filters {
  margin-left: auto;
  background-color: transparent;
  border: none;
  color: #6c757d;
  cursor: pointer;
  text-decoration: underline;
  font-size: 14px;
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
}

.summary-card h3 {
  margin-bottom: 8px;
  font-size: 16px;
  color: #6c757d;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #4a6cf7;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 16px;
}

.chart-card h3 {
  margin-bottom: 16px;
  text-align: center;
  color: #333;
}

.chart-container {
  height: 300px;
}

.tables-container {
  margin-bottom: 24px;
}

.table-card {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 16px;
  overflow-x: auto;
}

.table-card h3 {
  margin-bottom: 16px;
  text-align: center;
  color: #333;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
}

.report-table th,
.report-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.report-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.report-table tr:hover {
  background-color: #f8f9fa;
}

.report-table tr:last-child td {
  border-bottom: none;
}

.export-section {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
}

.export-section h3 {
  margin-bottom: 8px;
}

.export-section p {
  margin-bottom: 16px;
  color: #6c757d;
}

.export-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 16px;
}

.export-button:hover {
  background-color: #3a5bd9;
}

/* Loading spinner styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(74, 108, 247, 0.2);
  border-radius: 50%;
  border-top-color: #4a6cf7;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 16px;
  }

  .charts-container {
    grid-template-columns: 1fr;
  }

  .report-summary {
    grid-template-columns: repeat(2, 1fr);
  }
}
