/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace certificatemanager_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Certificate Manager API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const certificatemanager = google.certificatemanager('v1');
     * ```
     */
    export class Certificatemanager {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Defines an allowlisted certificate.
     */
    export interface Schema$AllowlistedCertificate {
        /**
         * Required. PEM certificate that is allowlisted. The certificate can be up to 5k bytes, and must be a parseable X.509 certificate.
         */
        pemCertificate?: string | null;
    }
    /**
     * State of the latest attempt to authorize a domain for certificate issuance.
     */
    export interface Schema$AuthorizationAttemptInfo {
        /**
         * Output only. Human readable explanation for reaching the state. Provided to help address the configuration issues. Not guaranteed to be stable. For programmatic access use FailureReason enum.
         */
        details?: string | null;
        /**
         * Output only. Domain name of the authorization attempt.
         */
        domain?: string | null;
        /**
         * Output only. Reason for failure of the authorization attempt for the domain.
         */
        failureReason?: string | null;
        /**
         * Output only. State of the domain for managed certificate issuance.
         */
        state?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Defines TLS certificate.
     */
    export interface Schema$Certificate {
        /**
         * Output only. The creation timestamp of a Certificate.
         */
        createTime?: string | null;
        /**
         * Optional. One or more paragraphs of text description of a certificate.
         */
        description?: string | null;
        /**
         * Output only. The expiry timestamp of a Certificate.
         */
        expireTime?: string | null;
        /**
         * Optional. Set of labels associated with a Certificate.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * If set, contains configuration and state of a managed certificate.
         */
        managed?: Schema$ManagedCertificate;
        /**
         * Identifier. A user-defined name of the certificate. Certificate names must be unique globally and match pattern `projects/x/locations/x/certificates/x`.
         */
        name?: string | null;
        /**
         * Output only. The PEM-encoded certificate chain.
         */
        pemCertificate?: string | null;
        /**
         * Output only. The list of Subject Alternative Names of dnsName type defined in the certificate (see RFC 5280 *******). Managed certificates that haven't been provisioned yet have this field populated with a value of the managed.domains field.
         */
        sanDnsnames?: string[] | null;
        /**
         * Optional. Immutable. The scope of the certificate.
         */
        scope?: string | null;
        /**
         * If set, defines data of a self-managed certificate.
         */
        selfManaged?: Schema$SelfManagedCertificate;
        /**
         * Output only. The last update timestamp of a Certificate.
         */
        updateTime?: string | null;
        /**
         * Output only. The list of resources that use this Certificate.
         */
        usedBy?: Schema$UsedBy[];
    }
    /**
     * The CA that issues the workload certificate. It includes CA address, type, authentication to CA service, etc.
     */
    export interface Schema$CertificateAuthorityConfig {
        /**
         * Defines a CertificateAuthorityServiceConfig.
         */
        certificateAuthorityServiceConfig?: Schema$CertificateAuthorityServiceConfig;
    }
    /**
     * Contains information required to contact CA service.
     */
    export interface Schema$CertificateAuthorityServiceConfig {
        /**
         * Required. A CA pool resource used to issue a certificate. The CA pool string has a relative resource path following the form "projects/{project\}/locations/{location\}/caPools/{ca_pool\}".
         */
        caPool?: string | null;
    }
    /**
     * CertificateIssuanceConfig specifies how to issue and manage a certificate.
     */
    export interface Schema$CertificateIssuanceConfig {
        /**
         * Required. The CA that issues the workload certificate. It includes the CA address, type, authentication to CA service, etc.
         */
        certificateAuthorityConfig?: Schema$CertificateAuthorityConfig;
        /**
         * Output only. The creation timestamp of a CertificateIssuanceConfig.
         */
        createTime?: string | null;
        /**
         * Optional. One or more paragraphs of text description of a CertificateIssuanceConfig.
         */
        description?: string | null;
        /**
         * Required. The key algorithm to use when generating the private key.
         */
        keyAlgorithm?: string | null;
        /**
         * Optional. Set of labels associated with a CertificateIssuanceConfig.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Workload certificate lifetime requested.
         */
        lifetime?: string | null;
        /**
         * Identifier. A user-defined name of the certificate issuance config. CertificateIssuanceConfig names must be unique globally and match pattern `projects/x/locations/x/certificateIssuanceConfigs/x`.
         */
        name?: string | null;
        /**
         * Required. Specifies the percentage of elapsed time of the certificate lifetime to wait before renewing the certificate. Must be a number between 1-99, inclusive.
         */
        rotationWindowPercentage?: number | null;
        /**
         * Output only. The last update timestamp of a CertificateIssuanceConfig.
         */
        updateTime?: string | null;
    }
    /**
     * Defines a collection of certificate configurations.
     */
    export interface Schema$CertificateMap {
        /**
         * Output only. The creation timestamp of a Certificate Map.
         */
        createTime?: string | null;
        /**
         * Optional. One or more paragraphs of text description of a certificate map.
         */
        description?: string | null;
        /**
         * Output only. A list of GCLB targets that use this Certificate Map. A Target Proxy is only present on this list if it's attached to a Forwarding Rule.
         */
        gclbTargets?: Schema$GclbTarget[];
        /**
         * Optional. Set of labels associated with a Certificate Map.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Identifier. A user-defined name of the Certificate Map. Certificate Map names must be unique globally and match pattern `projects/x/locations/x/certificateMaps/x`.
         */
        name?: string | null;
        /**
         * Output only. The update timestamp of a Certificate Map.
         */
        updateTime?: string | null;
    }
    /**
     * Defines a certificate map entry.
     */
    export interface Schema$CertificateMapEntry {
        /**
         * Optional. A set of Certificates defines for the given `hostname`. There can be defined up to four certificates in each Certificate Map Entry. Each certificate must match pattern `projects/x/locations/x/certificates/x`.
         */
        certificates?: string[] | null;
        /**
         * Output only. The creation timestamp of a Certificate Map Entry.
         */
        createTime?: string | null;
        /**
         * Optional. One or more paragraphs of text description of a certificate map entry.
         */
        description?: string | null;
        /**
         * A Hostname (FQDN, e.g. `example.com`) or a wildcard hostname expression (`*.example.com`) for a set of hostnames with common suffix. Used as Server Name Indication (SNI) for selecting a proper certificate.
         */
        hostname?: string | null;
        /**
         * Optional. Set of labels associated with a Certificate Map Entry.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * A predefined matcher for particular cases, other than SNI selection.
         */
        matcher?: string | null;
        /**
         * Identifier. A user-defined name of the Certificate Map Entry. Certificate Map Entry names must be unique globally and match pattern `projects/x/locations/x/certificateMaps/x/certificateMapEntries/x`.
         */
        name?: string | null;
        /**
         * Output only. A serving state of this Certificate Map Entry.
         */
        state?: string | null;
        /**
         * Output only. The update timestamp of a Certificate Map Entry.
         */
        updateTime?: string | null;
    }
    /**
     * A DnsAuthorization resource describes a way to perform domain authorization for certificate issuance.
     */
    export interface Schema$DnsAuthorization {
        /**
         * Output only. The creation timestamp of a DnsAuthorization.
         */
        createTime?: string | null;
        /**
         * Optional. One or more paragraphs of text description of a DnsAuthorization.
         */
        description?: string | null;
        /**
         * Output only. DNS Resource Record that needs to be added to DNS configuration.
         */
        dnsResourceRecord?: Schema$DnsResourceRecord;
        /**
         * Required. Immutable. A domain that is being authorized. A DnsAuthorization resource covers a single domain and its wildcard, e.g. authorization for `example.com` can be used to issue certificates for `example.com` and `*.example.com`.
         */
        domain?: string | null;
        /**
         * Optional. Set of labels associated with a DnsAuthorization.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Identifier. A user-defined name of the dns authorization. DnsAuthorization names must be unique globally and match pattern `projects/x/locations/x/dnsAuthorizations/x`.
         */
        name?: string | null;
        /**
         * Optional. Immutable. Type of DnsAuthorization. If unset during resource creation the following default will be used: - in location `global`: FIXED_RECORD, - in other locations: PER_PROJECT_RECORD.
         */
        type?: string | null;
        /**
         * Output only. The last update timestamp of a DnsAuthorization.
         */
        updateTime?: string | null;
    }
    /**
     * The structure describing the DNS Resource Record that needs to be added to DNS configuration for the authorization to be usable by certificate.
     */
    export interface Schema$DnsResourceRecord {
        /**
         * Output only. Data of the DNS Resource Record.
         */
        data?: string | null;
        /**
         * Output only. Fully qualified name of the DNS Resource Record. e.g. `_acme-challenge.example.com`
         */
        name?: string | null;
        /**
         * Output only. Type of the DNS Resource Record. Currently always set to "CNAME".
         */
        type?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Describes a Target Proxy that uses this Certificate Map.
     */
    export interface Schema$GclbTarget {
        /**
         * Output only. IP configurations for this Target Proxy where the Certificate Map is serving.
         */
        ipConfigs?: Schema$IpConfig[];
        /**
         * Output only. This field returns the resource name in the following format: `//compute.googleapis.com/projects/x/global/targetHttpsProxies/x`.
         */
        targetHttpsProxy?: string | null;
        /**
         * Output only. This field returns the resource name in the following format: `//compute.googleapis.com/projects/x/global/targetSslProxies/x`.
         */
        targetSslProxy?: string | null;
    }
    /**
     * Defines an intermediate CA.
     */
    export interface Schema$IntermediateCA {
        /**
         * PEM intermediate certificate used for building up paths for validation. Each certificate provided in PEM format may occupy up to 5kB.
         */
        pemCertificate?: string | null;
    }
    /**
     * Defines IP configuration where this Certificate Map is serving.
     */
    export interface Schema$IpConfig {
        /**
         * Output only. An external IP address.
         */
        ipAddress?: string | null;
        /**
         * Output only. Ports.
         */
        ports?: number[] | null;
    }
    /**
     * Response for the `ListCertificateIssuanceConfigs` method.
     */
    export interface Schema$ListCertificateIssuanceConfigsResponse {
        /**
         * A list of certificate configs for the parent resource.
         */
        certificateIssuanceConfigs?: Schema$CertificateIssuanceConfig[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for the `ListCertificateMapEntries` method.
     */
    export interface Schema$ListCertificateMapEntriesResponse {
        /**
         * A list of certificate map entries for the parent resource.
         */
        certificateMapEntries?: Schema$CertificateMapEntry[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for the `ListCertificateMaps` method.
     */
    export interface Schema$ListCertificateMapsResponse {
        /**
         * A list of certificate maps for the parent resource.
         */
        certificateMaps?: Schema$CertificateMap[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for the `ListCertificates` method.
     */
    export interface Schema$ListCertificatesResponse {
        /**
         * A list of certificates for the parent resource.
         */
        certificates?: Schema$Certificate[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for the `ListDnsAuthorizations` method.
     */
    export interface Schema$ListDnsAuthorizationsResponse {
        /**
         * A list of dns authorizations for the parent resource.
         */
        dnsAuthorizations?: Schema$DnsAuthorization[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response for the `ListTrustConfigs` method.
     */
    export interface Schema$ListTrustConfigsResponse {
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * A list of TrustConfigs for the parent resource.
         */
        trustConfigs?: Schema$TrustConfig[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Configuration and state of a Managed Certificate. Certificate Manager provisions and renews Managed Certificates automatically, for as long as it's authorized to do so.
     */
    export interface Schema$ManagedCertificate {
        /**
         * Output only. Detailed state of the latest authorization attempt for each domain specified for managed certificate resource.
         */
        authorizationAttemptInfo?: Schema$AuthorizationAttemptInfo[];
        /**
         * Optional. Immutable. Authorizations that will be used for performing domain authorization.
         */
        dnsAuthorizations?: string[] | null;
        /**
         * Optional. Immutable. The domains for which a managed SSL certificate will be generated. Wildcard domains are only supported with DNS challenge resolution.
         */
        domains?: string[] | null;
        /**
         * Optional. Immutable. The resource name for a CertificateIssuanceConfig used to configure private PKI certificates in the format `projects/x/locations/x/certificateIssuanceConfigs/x`. If this field is not set, the certificates will instead be publicly signed as documented at https://cloud.google.com/load-balancing/docs/ssl-certificates/google-managed-certs#caa.
         */
        issuanceConfig?: string | null;
        /**
         * Output only. Information about issues with provisioning a Managed Certificate.
         */
        provisioningIssue?: Schema$ProvisioningIssue;
        /**
         * Output only. State of the managed certificate resource.
         */
        state?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation. Output only.
     */
    export interface Schema$OperationMetadata {
        /**
         * API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * The time the operation was created.
         */
        createTime?: string | null;
        /**
         * The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Information about issues with provisioning a Managed Certificate.
     */
    export interface Schema$ProvisioningIssue {
        /**
         * Output only. Human readable explanation about the issue. Provided to help address the configuration issues. Not guaranteed to be stable. For programmatic access use Reason enum.
         */
        details?: string | null;
        /**
         * Output only. Reason for provisioning failures.
         */
        reason?: string | null;
    }
    /**
     * Certificate data for a SelfManaged Certificate. SelfManaged Certificates are uploaded by the user. Updating such certificates before they expire remains the user's responsibility.
     */
    export interface Schema$SelfManagedCertificate {
        /**
         * Optional. Input only. The PEM-encoded certificate chain. Leaf certificate comes first, followed by intermediate ones if any.
         */
        pemCertificate?: string | null;
        /**
         * Optional. Input only. The PEM-encoded private key of the leaf certificate.
         */
        pemPrivateKey?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Defines a trust anchor.
     */
    export interface Schema$TrustAnchor {
        /**
         * PEM root certificate of the PKI used for validation. Each certificate provided in PEM format may occupy up to 5kB.
         */
        pemCertificate?: string | null;
    }
    /**
     * Defines a trust config.
     */
    export interface Schema$TrustConfig {
        /**
         * Optional. A certificate matching an allowlisted certificate is always considered valid as long as the certificate is parseable, proof of private key possession is established, and constraints on the certificate's SAN field are met.
         */
        allowlistedCertificates?: Schema$AllowlistedCertificate[];
        /**
         * Output only. The creation timestamp of a TrustConfig.
         */
        createTime?: string | null;
        /**
         * Optional. One or more paragraphs of text description of a TrustConfig.
         */
        description?: string | null;
        /**
         * This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Optional. Set of labels associated with a TrustConfig.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Identifier. A user-defined name of the trust config. TrustConfig names must be unique globally and match pattern `projects/x/locations/x/trustConfigs/x`.
         */
        name?: string | null;
        /**
         * Optional. Set of trust stores to perform validation against. This field is supported when TrustConfig is configured with Load Balancers, currently not supported for SPIFFE certificate validation. Only one TrustStore specified is currently allowed.
         */
        trustStores?: Schema$TrustStore[];
        /**
         * Output only. The last update timestamp of a TrustConfig.
         */
        updateTime?: string | null;
    }
    /**
     * Defines a trust store.
     */
    export interface Schema$TrustStore {
        /**
         * Optional. Set of intermediate CA certificates used for the path building phase of chain validation. The field is currently not supported if TrustConfig is used for the workload certificate feature.
         */
        intermediateCas?: Schema$IntermediateCA[];
        /**
         * Optional. List of Trust Anchors to be used while performing validation against a given TrustStore.
         */
        trustAnchors?: Schema$TrustAnchor[];
    }
    /**
     * Defines a resource that uses the certificate.
     */
    export interface Schema$UsedBy {
        /**
         * Output only. Full name of the resource https://google.aip.dev/122#full-resource-names, e.g. `//certificatemanager.googleapis.com/projects/x/locations/x/certificateMaps/x/certificateMapEntries/x` or `//compute.googleapis.com/projects/x/locations/x/targetHttpsProxies/x`.
         */
        name?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        certificateIssuanceConfigs: Resource$Projects$Locations$Certificateissuanceconfigs;
        certificateMaps: Resource$Projects$Locations$Certificatemaps;
        certificates: Resource$Projects$Locations$Certificates;
        dnsAuthorizations: Resource$Projects$Locations$Dnsauthorizations;
        operations: Resource$Projects$Locations$Operations;
        trustConfigs: Resource$Projects$Locations$Trustconfigs;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Certificateissuanceconfigs {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new CertificateIssuanceConfig in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single CertificateIssuanceConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single CertificateIssuanceConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Get, options?: MethodOptions): GaxiosPromise<Schema$CertificateIssuanceConfig>;
        get(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Get, options: MethodOptions | BodyResponseCallback<Schema$CertificateIssuanceConfig>, callback: BodyResponseCallback<Schema$CertificateIssuanceConfig>): void;
        get(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Get, callback: BodyResponseCallback<Schema$CertificateIssuanceConfig>): void;
        get(callback: BodyResponseCallback<Schema$CertificateIssuanceConfig>): void;
        /**
         * Lists CertificateIssuanceConfigs in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Certificateissuanceconfigs$List, options?: MethodOptions): GaxiosPromise<Schema$ListCertificateIssuanceConfigsResponse>;
        list(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$List, options: MethodOptions | BodyResponseCallback<Schema$ListCertificateIssuanceConfigsResponse>, callback: BodyResponseCallback<Schema$ListCertificateIssuanceConfigsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$List, callback: BodyResponseCallback<Schema$ListCertificateIssuanceConfigsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListCertificateIssuanceConfigsResponse>): void;
        /**
         * Updates a CertificateIssuanceConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Certificateissuanceconfigs$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Certificateissuanceconfigs$Create extends StandardParameters {
        /**
         * Required. A user-provided name of the certificate config.
         */
        certificateIssuanceConfigId?: string;
        /**
         * Required. The parent resource of the certificate issuance config. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CertificateIssuanceConfig;
    }
    export interface Params$Resource$Projects$Locations$Certificateissuanceconfigs$Delete extends StandardParameters {
        /**
         * Required. A name of the certificate issuance config to delete. Must be in the format `projects/x/locations/x/certificateIssuanceConfigs/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificateissuanceconfigs$Get extends StandardParameters {
        /**
         * Required. A name of the certificate issuance config to describe. Must be in the format `projects/x/locations/x/certificateIssuanceConfigs/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificateissuanceconfigs$List extends StandardParameters {
        /**
         * Optional. Filter expression to restrict the Certificates Configs returned.
         */
        filter?: string;
        /**
         * Optional. A list of Certificate Config field names used to specify the order of the returned results. The default sorting order is ascending. To specify descending order for a field, add a suffix `" desc"`.
         */
        orderBy?: string;
        /**
         * Optional. Maximum number of certificate configs to return per call.
         */
        pageSize?: number;
        /**
         * Optional. The value returned by the last `ListCertificateIssuanceConfigsResponse`. Indicates that this is a continuation of a prior `ListCertificateIssuanceConfigs` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the certificate should be listed, specified in the format `projects/x/locations/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificateissuanceconfigs$Patch extends StandardParameters {
        /**
         * Identifier. A user-defined name of the certificate issuance config. CertificateIssuanceConfig names must be unique globally and match pattern `projects/x/locations/x/certificateIssuanceConfigs/x`.
         */
        name?: string;
        /**
         * Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CertificateIssuanceConfig;
    }
    export class Resource$Projects$Locations$Certificatemaps {
        context: APIRequestContext;
        certificateMapEntries: Resource$Projects$Locations$Certificatemaps$Certificatemapentries;
        constructor(context: APIRequestContext);
        /**
         * Creates a new CertificateMap in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Certificatemaps$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single CertificateMap. A Certificate Map can't be deleted if it contains Certificate Map Entries. Remove all the entries from the map before calling this method.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Certificatemaps$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single CertificateMap.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Certificatemaps$Get, options?: MethodOptions): GaxiosPromise<Schema$CertificateMap>;
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Get, options: MethodOptions | BodyResponseCallback<Schema$CertificateMap>, callback: BodyResponseCallback<Schema$CertificateMap>): void;
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Get, callback: BodyResponseCallback<Schema$CertificateMap>): void;
        get(callback: BodyResponseCallback<Schema$CertificateMap>): void;
        /**
         * Lists CertificateMaps in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Certificatemaps$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Certificatemaps$List, options?: MethodOptions): GaxiosPromise<Schema$ListCertificateMapsResponse>;
        list(params: Params$Resource$Projects$Locations$Certificatemaps$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Certificatemaps$List, options: MethodOptions | BodyResponseCallback<Schema$ListCertificateMapsResponse>, callback: BodyResponseCallback<Schema$ListCertificateMapsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Certificatemaps$List, callback: BodyResponseCallback<Schema$ListCertificateMapsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListCertificateMapsResponse>): void;
        /**
         * Updates a CertificateMap.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Certificatemaps$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Create extends StandardParameters {
        /**
         * Required. A user-provided name of the certificate map.
         */
        certificateMapId?: string;
        /**
         * Required. The parent resource of the certificate map. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CertificateMap;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Delete extends StandardParameters {
        /**
         * Required. A name of the certificate map to delete. Must be in the format `projects/x/locations/x/certificateMaps/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Get extends StandardParameters {
        /**
         * Required. A name of the certificate map to describe. Must be in the format `projects/x/locations/x/certificateMaps/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$List extends StandardParameters {
        /**
         * Optional. Filter expression to restrict the Certificates Maps returned.
         */
        filter?: string;
        /**
         * Optional. A list of Certificate Map field names used to specify the order of the returned results. The default sorting order is ascending. To specify descending order for a field, add a suffix `" desc"`.
         */
        orderBy?: string;
        /**
         * Optional. Maximum number of certificate maps to return per call.
         */
        pageSize?: number;
        /**
         * Optional. The value returned by the last `ListCertificateMapsResponse`. Indicates that this is a continuation of a prior `ListCertificateMaps` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the certificate maps should be listed, specified in the format `projects/x/locations/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Patch extends StandardParameters {
        /**
         * Identifier. A user-defined name of the Certificate Map. Certificate Map names must be unique globally and match pattern `projects/x/locations/x/certificateMaps/x`.
         */
        name?: string;
        /**
         * Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CertificateMap;
    }
    export class Resource$Projects$Locations$Certificatemaps$Certificatemapentries {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new CertificateMapEntry in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single CertificateMapEntry.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single CertificateMapEntry.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Get, options?: MethodOptions): GaxiosPromise<Schema$CertificateMapEntry>;
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Get, options: MethodOptions | BodyResponseCallback<Schema$CertificateMapEntry>, callback: BodyResponseCallback<Schema$CertificateMapEntry>): void;
        get(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Get, callback: BodyResponseCallback<Schema$CertificateMapEntry>): void;
        get(callback: BodyResponseCallback<Schema$CertificateMapEntry>): void;
        /**
         * Lists CertificateMapEntries in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$List, options?: MethodOptions): GaxiosPromise<Schema$ListCertificateMapEntriesResponse>;
        list(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$List, options: MethodOptions | BodyResponseCallback<Schema$ListCertificateMapEntriesResponse>, callback: BodyResponseCallback<Schema$ListCertificateMapEntriesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$List, callback: BodyResponseCallback<Schema$ListCertificateMapEntriesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListCertificateMapEntriesResponse>): void;
        /**
         * Updates a CertificateMapEntry.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Create extends StandardParameters {
        /**
         * Required. A user-provided name of the certificate map entry.
         */
        certificateMapEntryId?: string;
        /**
         * Required. The parent resource of the certificate map entry. Must be in the format `projects/x/locations/x/certificateMaps/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CertificateMapEntry;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Delete extends StandardParameters {
        /**
         * Required. A name of the certificate map entry to delete. Must be in the format `projects/x/locations/x/certificateMaps/x/certificateMapEntries/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Get extends StandardParameters {
        /**
         * Required. A name of the certificate map entry to describe. Must be in the format `projects/x/locations/x/certificateMaps/x/certificateMapEntries/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$List extends StandardParameters {
        /**
         * Optional. Filter expression to restrict the returned Certificate Map Entries.
         */
        filter?: string;
        /**
         * Optional. A list of Certificate Map Entry field names used to specify the order of the returned results. The default sorting order is ascending. To specify descending order for a field, add a suffix `" desc"`.
         */
        orderBy?: string;
        /**
         * Optional. Maximum number of certificate map entries to return. The service may return fewer than this value. If unspecified, at most 50 certificate map entries will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. The value returned by the last `ListCertificateMapEntriesResponse`. Indicates that this is a continuation of a prior `ListCertificateMapEntries` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project, location and certificate map from which the certificate map entries should be listed, specified in the format `projects/x/locations/x/certificateMaps/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificatemaps$Certificatemapentries$Patch extends StandardParameters {
        /**
         * Identifier. A user-defined name of the Certificate Map Entry. Certificate Map Entry names must be unique globally and match pattern `projects/x/locations/x/certificateMaps/x/certificateMapEntries/x`.
         */
        name?: string;
        /**
         * Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CertificateMapEntry;
    }
    export class Resource$Projects$Locations$Certificates {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Certificate in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Certificates$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Certificates$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Certificates$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Certificates$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Certificates$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Certificates$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Certificates$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Certificates$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Certificates$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Certificates$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Certificates$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Certificates$Get, options?: MethodOptions): GaxiosPromise<Schema$Certificate>;
        get(params: Params$Resource$Projects$Locations$Certificates$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Certificates$Get, options: MethodOptions | BodyResponseCallback<Schema$Certificate>, callback: BodyResponseCallback<Schema$Certificate>): void;
        get(params: Params$Resource$Projects$Locations$Certificates$Get, callback: BodyResponseCallback<Schema$Certificate>): void;
        get(callback: BodyResponseCallback<Schema$Certificate>): void;
        /**
         * Lists Certificates in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Certificates$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Certificates$List, options?: MethodOptions): GaxiosPromise<Schema$ListCertificatesResponse>;
        list(params: Params$Resource$Projects$Locations$Certificates$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Certificates$List, options: MethodOptions | BodyResponseCallback<Schema$ListCertificatesResponse>, callback: BodyResponseCallback<Schema$ListCertificatesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Certificates$List, callback: BodyResponseCallback<Schema$ListCertificatesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListCertificatesResponse>): void;
        /**
         * Updates a Certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Certificates$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Certificates$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Certificates$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Certificates$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Certificates$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Certificates$Create extends StandardParameters {
        /**
         * Required. A user-provided name of the certificate.
         */
        certificateId?: string;
        /**
         * Required. The parent resource of the certificate. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Certificate;
    }
    export interface Params$Resource$Projects$Locations$Certificates$Delete extends StandardParameters {
        /**
         * Required. A name of the certificate to delete. Must be in the format `projects/x/locations/x/certificates/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificates$Get extends StandardParameters {
        /**
         * Required. A name of the certificate to describe. Must be in the format `projects/x/locations/x/certificates/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificates$List extends StandardParameters {
        /**
         * Optional. Filter expression to restrict the Certificates returned.
         */
        filter?: string;
        /**
         * Optional. A list of Certificate field names used to specify the order of the returned results. The default sorting order is ascending. To specify descending order for a field, add a suffix `" desc"`.
         */
        orderBy?: string;
        /**
         * Optional. Maximum number of certificates to return per call.
         */
        pageSize?: number;
        /**
         * Optional. The value returned by the last `ListCertificatesResponse`. Indicates that this is a continuation of a prior `ListCertificates` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the certificate should be listed, specified in the format `projects/x/locations/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Certificates$Patch extends StandardParameters {
        /**
         * Identifier. A user-defined name of the certificate. Certificate names must be unique globally and match pattern `projects/x/locations/x/certificates/x`.
         */
        name?: string;
        /**
         * Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Certificate;
    }
    export class Resource$Projects$Locations$Dnsauthorizations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new DnsAuthorization in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Dnsauthorizations$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Dnsauthorizations$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Dnsauthorizations$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Dnsauthorizations$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Dnsauthorizations$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single DnsAuthorization.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Dnsauthorizations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Dnsauthorizations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Dnsauthorizations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Dnsauthorizations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Dnsauthorizations$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single DnsAuthorization.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Dnsauthorizations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Dnsauthorizations$Get, options?: MethodOptions): GaxiosPromise<Schema$DnsAuthorization>;
        get(params: Params$Resource$Projects$Locations$Dnsauthorizations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Dnsauthorizations$Get, options: MethodOptions | BodyResponseCallback<Schema$DnsAuthorization>, callback: BodyResponseCallback<Schema$DnsAuthorization>): void;
        get(params: Params$Resource$Projects$Locations$Dnsauthorizations$Get, callback: BodyResponseCallback<Schema$DnsAuthorization>): void;
        get(callback: BodyResponseCallback<Schema$DnsAuthorization>): void;
        /**
         * Lists DnsAuthorizations in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Dnsauthorizations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Dnsauthorizations$List, options?: MethodOptions): GaxiosPromise<Schema$ListDnsAuthorizationsResponse>;
        list(params: Params$Resource$Projects$Locations$Dnsauthorizations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Dnsauthorizations$List, options: MethodOptions | BodyResponseCallback<Schema$ListDnsAuthorizationsResponse>, callback: BodyResponseCallback<Schema$ListDnsAuthorizationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Dnsauthorizations$List, callback: BodyResponseCallback<Schema$ListDnsAuthorizationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDnsAuthorizationsResponse>): void;
        /**
         * Updates a DnsAuthorization.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Dnsauthorizations$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Dnsauthorizations$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Dnsauthorizations$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Dnsauthorizations$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Dnsauthorizations$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Dnsauthorizations$Create extends StandardParameters {
        /**
         * Required. A user-provided name of the dns authorization.
         */
        dnsAuthorizationId?: string;
        /**
         * Required. The parent resource of the dns authorization. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DnsAuthorization;
    }
    export interface Params$Resource$Projects$Locations$Dnsauthorizations$Delete extends StandardParameters {
        /**
         * Required. A name of the dns authorization to delete. Must be in the format `projects/x/locations/x/dnsAuthorizations/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Dnsauthorizations$Get extends StandardParameters {
        /**
         * Required. A name of the dns authorization to describe. Must be in the format `projects/x/locations/x/dnsAuthorizations/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Dnsauthorizations$List extends StandardParameters {
        /**
         * Optional. Filter expression to restrict the Dns Authorizations returned.
         */
        filter?: string;
        /**
         * Optional. A list of Dns Authorization field names used to specify the order of the returned results. The default sorting order is ascending. To specify descending order for a field, add a suffix `" desc"`.
         */
        orderBy?: string;
        /**
         * Optional. Maximum number of dns authorizations to return per call.
         */
        pageSize?: number;
        /**
         * Optional. The value returned by the last `ListDnsAuthorizationsResponse`. Indicates that this is a continuation of a prior `ListDnsAuthorizations` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the dns authorizations should be listed, specified in the format `projects/x/locations/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Dnsauthorizations$Patch extends StandardParameters {
        /**
         * Identifier. A user-defined name of the dns authorization. DnsAuthorization names must be unique globally and match pattern `projects/x/locations/x/dnsAuthorizations/x`.
         */
        name?: string;
        /**
         * Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DnsAuthorization;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Trustconfigs {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new TrustConfig in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Trustconfigs$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Trustconfigs$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Trustconfigs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Trustconfigs$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Trustconfigs$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single TrustConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Trustconfigs$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Trustconfigs$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Trustconfigs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Trustconfigs$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Trustconfigs$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single TrustConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Trustconfigs$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Trustconfigs$Get, options?: MethodOptions): GaxiosPromise<Schema$TrustConfig>;
        get(params: Params$Resource$Projects$Locations$Trustconfigs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Trustconfigs$Get, options: MethodOptions | BodyResponseCallback<Schema$TrustConfig>, callback: BodyResponseCallback<Schema$TrustConfig>): void;
        get(params: Params$Resource$Projects$Locations$Trustconfigs$Get, callback: BodyResponseCallback<Schema$TrustConfig>): void;
        get(callback: BodyResponseCallback<Schema$TrustConfig>): void;
        /**
         * Lists TrustConfigs in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Trustconfigs$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Trustconfigs$List, options?: MethodOptions): GaxiosPromise<Schema$ListTrustConfigsResponse>;
        list(params: Params$Resource$Projects$Locations$Trustconfigs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Trustconfigs$List, options: MethodOptions | BodyResponseCallback<Schema$ListTrustConfigsResponse>, callback: BodyResponseCallback<Schema$ListTrustConfigsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Trustconfigs$List, callback: BodyResponseCallback<Schema$ListTrustConfigsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTrustConfigsResponse>): void;
        /**
         * Updates a TrustConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Trustconfigs$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Trustconfigs$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Trustconfigs$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Trustconfigs$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Trustconfigs$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Trustconfigs$Create extends StandardParameters {
        /**
         * Required. The parent resource of the TrustConfig. Must be in the format `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Required. A user-provided name of the TrustConfig. Must match the regexp `[a-z0-9-]{1,63\}`.
         */
        trustConfigId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TrustConfig;
    }
    export interface Params$Resource$Projects$Locations$Trustconfigs$Delete extends StandardParameters {
        /**
         * Optional. The current etag of the TrustConfig. If an etag is provided and does not match the current etag of the resource, deletion will be blocked and an ABORTED error will be returned.
         */
        etag?: string;
        /**
         * Required. A name of the TrustConfig to delete. Must be in the format `projects/x/locations/x/trustConfigs/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Trustconfigs$Get extends StandardParameters {
        /**
         * Required. A name of the TrustConfig to describe. Must be in the format `projects/x/locations/x/trustConfigs/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Trustconfigs$List extends StandardParameters {
        /**
         * Optional. Filter expression to restrict the TrustConfigs returned.
         */
        filter?: string;
        /**
         * Optional. A list of TrustConfig field names used to specify the order of the returned results. The default sorting order is ascending. To specify descending order for a field, add a suffix `" desc"`.
         */
        orderBy?: string;
        /**
         * Optional. Maximum number of TrustConfigs to return per call.
         */
        pageSize?: number;
        /**
         * Optional. The value returned by the last `ListTrustConfigsResponse`. Indicates that this is a continuation of a prior `ListTrustConfigs` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the TrustConfigs should be listed, specified in the format `projects/x/locations/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Trustconfigs$Patch extends StandardParameters {
        /**
         * Identifier. A user-defined name of the trust config. TrustConfig names must be unique globally and match pattern `projects/x/locations/x/trustConfigs/x`.
         */
        name?: string;
        /**
         * Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TrustConfig;
    }
    export {};
}
